/**
 * RealtimeVoiceRecognitionExample.ts
 * 实时语音识别使用示例
 * 
 * 展示如何集成和使用 RealtimeVoiceRecognition 类
 */

import { BrowserWindow, ipcMain } from 'electron';
import { RealtimeVoiceRecognition, AudioData, TranscriptionResult, VoiceConfig } from './RealtimeVoiceRecognition';

/**
 * 实时语音识别服务管理器
 */
export class VoiceRecognitionService {
  private voiceRecognition: RealtimeVoiceRecognition;
  private mainWindow: BrowserWindow | null = null;
  private isInitialized: boolean = false;

  constructor() {
    this.voiceRecognition = new RealtimeVoiceRecognition();
    this.setupEventListeners();
  }

  /**
   * 初始化服务
   */
  public async initialize(mainWindow: BrowserWindow, config: VoiceConfig): Promise<void> {
    console.log('VoiceRecognitionService: 开始初始化...');
    
    this.mainWindow = mainWindow;
    this.voiceRecognition.setMainWindow(mainWindow);
    this.voiceRecognition.setConfig(config);
    
    // 注册IPC处理程序
    this.registerIPCHandlers();
    
    this.isInitialized = true;
    console.log('VoiceRecognitionService: 初始化完成');
  }

  /**
   * 启动语音识别
   */
  public async startRecognition(): Promise<{ success: boolean; error?: string }> {
    if (!this.isInitialized) {
      return { success: false, error: '服务未初始化' };
    }

    console.log('VoiceRecognitionService: 启动语音识别');
    return await this.voiceRecognition.startRecognition();
  }

  /**
   * 停止语音识别
   */
  public async stopRecognition(): Promise<void> {
    console.log('VoiceRecognitionService: 停止语音识别');
    await this.voiceRecognition.stopRecognition();
  }

  /**
   * 处理麦克风音频数据
   */
  public async processMicrophoneAudio(audioBuffer: ArrayBuffer, sampleRate: number = 16000): Promise<void> {
    const audioData: AudioData = {
      audio_data: audioBuffer,
      sample_rate: sampleRate,
      audio_format: 'pcm',
      source: 'microphone',
      timestamp: Date.now()
    };

    await this.voiceRecognition.processAudioData(audioData);
  }

  /**
   * 处理系统音频数据
   */
  public async processSystemAudio(audioBuffer: ArrayBuffer, sampleRate: number = 16000): Promise<void> {
    const audioData: AudioData = {
      audio_data: audioBuffer,
      sample_rate: sampleRate,
      audio_format: 'pcm',
      source: 'system',
      timestamp: Date.now()
    };

    await this.voiceRecognition.processAudioData(audioData);
  }

  /**
   * 获取格式化的转写文案（用于发送给AI）
   */
  public getFormattedTranscriptions(): string {
    return this.voiceRecognition.collectAndFormatTranscriptions();
  }

  /**
   * 获取当前状态
   */
  public getStatus() {
    return this.voiceRecognition.getStatus();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听转写结果
    this.voiceRecognition.on('transcription', (result: TranscriptionResult) => {
      console.log(`📝 转写结果 [${result.source}]:`, result.text);
      
      // 可以在这里添加自定义处理逻辑
      if (result.isFinal) {
        this.handleFinalTranscription(result);
      } else {
        this.handleInterimTranscription(result);
      }
    });

    // 监听状态变化
    this.voiceRecognition.on('statusChange', (status) => {
      console.log('🔄 状态变化:', status);
      
      // 通知UI状态变化
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.send('voice-recognition:status-change', status);
      }
    });
  }

  /**
   * 处理最终转写结果
   */
  private handleFinalTranscription(result: TranscriptionResult): void {
    console.log(`✅ 最终转写 [${result.source}]: ${result.text}`);
    
    // 可以在这里添加特定的处理逻辑
    // 例如：保存到数据库、触发特定动作等
    
    // 检查是否包含特定关键词
    if (result.text.includes('请求AI') || result.text.includes('发送给AI')) {
      console.log('🤖 检测到AI请求关键词，准备发送给AI');
      this.handleAIRequest();
    }
  }

  /**
   * 处理临时转写结果
   */
  private handleInterimTranscription(result: TranscriptionResult): void {
    console.log(`⏳ 临时转写 [${result.source}]: ${result.text}`);
    
    // 临时结果的处理逻辑
    // 例如：实时显示、语音命令检测等
  }

  /**
   * 处理AI请求
   */
  private async handleAIRequest(): Promise<void> {
    const formattedText = this.getFormattedTranscriptions();
    
    if (!formattedText.trim()) {
      console.warn('⚠️ 没有可发送的转写内容');
      return;
    }

    console.log('🚀 准备发送给AI:', formattedText);
    
    // 通知UI发送给AI
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('voice-recognition:ai-request', {
        text: formattedText,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 注册IPC处理程序
   */
  private registerIPCHandlers(): void {
    // 启动语音识别
    ipcMain.handle('voice-recognition:start', async () => {
      return await this.startRecognition();
    });

    // 停止语音识别
    ipcMain.handle('voice-recognition:stop', async () => {
      await this.stopRecognition();
      return { success: true };
    });

    // 处理麦克风音频
    ipcMain.handle('voice-recognition:process-microphone-audio', async (event, audioData) => {
      try {
        await this.processMicrophoneAudio(audioData.buffer, audioData.sampleRate);
        return { success: true };
      } catch (error) {
        console.error('处理麦克风音频失败:', error);
        return { success: false, error: error instanceof Error ? error.message : '处理失败' };
      }
    });

    // 处理系统音频
    ipcMain.handle('voice-recognition:process-system-audio', async (event, audioData) => {
      try {
        await this.processSystemAudio(audioData.buffer, audioData.sampleRate);
        return { success: true };
      } catch (error) {
        console.error('处理系统音频失败:', error);
        return { success: false, error: error instanceof Error ? error.message : '处理失败' };
      }
    });

    // 获取格式化转写文案
    ipcMain.handle('voice-recognition:get-formatted-text', () => {
      return {
        success: true,
        text: this.getFormattedTranscriptions()
      };
    });

    // 获取状态
    ipcMain.handle('voice-recognition:get-status', () => {
      return {
        success: true,
        status: this.getStatus()
      };
    });

    console.log('VoiceRecognitionService: IPC处理程序已注册');
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    console.log('VoiceRecognitionService: 开始清理资源');
    
    // 移除IPC处理程序
    ipcMain.removeHandler('voice-recognition:start');
    ipcMain.removeHandler('voice-recognition:stop');
    ipcMain.removeHandler('voice-recognition:process-microphone-audio');
    ipcMain.removeHandler('voice-recognition:process-system-audio');
    ipcMain.removeHandler('voice-recognition:get-formatted-text');
    ipcMain.removeHandler('voice-recognition:get-status');
    
    // 清理语音识别实例
    this.voiceRecognition.cleanup();
    
    this.isInitialized = false;
    console.log('VoiceRecognitionService: 资源清理完成');
  }
}

/**
 * 使用示例
 */
export function createVoiceRecognitionExample(mainWindow: BrowserWindow): VoiceRecognitionService {
  const service = new VoiceRecognitionService();
  
  // 示例配置
  const config: VoiceConfig = {
    voiceAppId: 'your-app-id',
    voiceAccessKeyId: 'your-access-key',
    enableMicrophone: true,
    enableSystemAudio: true,
    silenceThreshold: 0.005,
    noiseGateThreshold: 0.003,
    maxBufferSize: 32000,
    minBufferSize: 6400
  };

  // 初始化服务
  service.initialize(mainWindow, config).then(() => {
    console.log('✅ 语音识别服务初始化完成');
    
    // 可以在这里启动语音识别
    // service.startRecognition();
  }).catch(error => {
    console.error('❌ 语音识别服务初始化失败:', error);
  });

  return service;
}

/**
 * 前端使用示例（在渲染进程中）
 */
export const frontendUsageExample = `
// 在渲染进程中使用语音识别服务

// 启动语音识别
const startRecognition = async () => {
  const result = await window.electronAPI.invoke('voice-recognition:start');
  if (result.success) {
    console.log('语音识别已启动');
  } else {
    console.error('启动失败:', result.error);
  }
};

// 停止语音识别
const stopRecognition = async () => {
  await window.electronAPI.invoke('voice-recognition:stop');
  console.log('语音识别已停止');
};

// 获取格式化的转写文案
const getFormattedText = async () => {
  const result = await window.electronAPI.invoke('voice-recognition:get-formatted-text');
  if (result.success) {
    console.log('格式化文案:', result.text);
    return result.text;
  }
  return '';
};

// 监听转写结果
window.electronAPI.on('asr:transcription', (data) => {
  console.log('收到转写结果:', data);
  
  if (data.isFinal) {
    // 处理最终结果
    handleFinalTranscription(data);
  } else {
    // 处理临时结果
    handleInterimTranscription(data);
  }
});

// 监听状态变化
window.electronAPI.on('voice-recognition:status-change', (status) => {
  console.log('状态变化:', status);
  updateUI(status);
});

// 点击"请求AI"按钮时的处理
const handleSendToAI = async () => {
  const formattedText = await getFormattedText();
  if (formattedText.trim()) {
    // 发送给AI处理
    sendToAI(formattedText);
  } else {
    console.warn('没有可发送的内容');
  }
};
`;
