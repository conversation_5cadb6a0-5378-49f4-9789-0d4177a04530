import { app as u, screen as f, <PERSON><PERSON><PERSON><PERSON>indow as ge, shell as X, ipcMain as U, session as W, desktopCapturer as Q, globalShortcut as E, systemPreferences as ce, protocol as we, net as U<PERSON>, <PERSON><PERSON> as le } from "electron";
import { PostHog as xe } from "posthog-node";
import { uuidv7 as Le } from "uuidv7";
import { electronApp as Fe } from "@electron-toolkit/utils";
import _, { join as N } from "node:path";
import { existsSync as me, writeFileSync as ye, unlinkSync as be } from "node:fs";
import o from "zod";
import { spawn as Z } from "node:child_process";
import { EventEmitter as $e } from "node:events";
import He from "electron-updater";
import { pathToFileURL as qe } from "node:url";
import { electronAppUniversalProtocolClient as de } from "electron-app-universal-protocol-client";
import Ve from "node:module";
const Gt = import.meta.filename, x = import.meta.dirname, Xt = Ve.createRequire(import.meta.url), L = new xe("phc_AXG9qwwTAPSJJ68tiYxIujNSztjw0Vm5J6tYpPdxiDh", {
  host: "https://us.i.posthog.com",
  // handles uncaught exceptions and unhandled rejections
  enableExceptionAutocapture: !0
});
L.register({
  appVersion: u.getVersion()
});
function je(s) {
  L?.identify({ distinctId: s });
}
function ze() {
  L?.identify({ distinctId: Le() });
}
function v(...s) {
  console.error(...s);
  const e = s.map((n) => {
    if (n !== null && typeof n == "object" && !(n instanceof Error))
      try {
        return JSON.stringify(n);
      } catch {
      }
    return String(n);
  }).join(" "), t = s.filter((n) => n instanceof Error).map((n) => ({
    name: n.name,
    message: n.message,
    stack: n.stack
  }));
  L.captureException(new Error(e), void 0, { referencedErrors: t });
}
const K = N(u.getPath("userData"), "onboarding.done");
let ee = me(K);
function ve() {
  return ee;
}
function Ge() {
  ye(K, ""), ee = !0, l.createOrRecreateWindow({ finishedOnboarding: !0 });
}
function Se() {
  try {
    be(K);
  } catch {
  }
  ee = !1, l.createOrRecreateWindow();
}
const I = process.platform === "darwin", C = process.platform === "win32";
process.platform;
const m = process.env.NODE_ENV === "development";
process.env.NODE_ENV;
function Xe(s, e, t, n) {
  e = Math.floor(e), t = Math.floor(t);
  const i = f.getPrimaryDisplay().displayFrequency;
  let d = Math.min(Math.max(i, 30), 360);
  i > 60 && (d = Math.max(60, Math.floor(i / 2)));
  const a = 1e3 / d, h = s.getBounds(), p = h.width, g = h.height, S = h.x, T = h.y, H = S + Math.floor((p - e) / 2), q = T + Math.floor((g - t) / 2), Ie = e - p, Be = t - g, Pe = H - S, ke = q - T, ne = Math.floor(n / a);
  let oe = 0;
  const We = Date.now();
  let b = null;
  const re = () => {
    const ae = Date.now() - We;
    if (oe = Math.min(ne, Math.floor(ae / a)), oe < ne) {
      const B = Ye(ae / n), V = Math.floor(p + Ie * B), j = Math.floor(g + Be * B), z = Math.floor(S + Pe * B), G = Math.floor(T + ke * B);
      if (C) {
        const P = s.getBounds();
        (Math.abs(P.width - V) >= 1 || Math.abs(P.height - j) >= 1 || Math.abs(P.x - z) >= 1 || Math.abs(P.y - G) >= 1) && s.setBounds(
          {
            x: z,
            y: G,
            width: V,
            height: j
          },
          !1
        );
      } else
        s.setBounds({
          x: z,
          y: G,
          width: V,
          height: j
        });
      b = setTimeout(re, a);
    } else
      s.setBounds({
        x: H,
        y: q,
        width: e,
        height: t
      }), s.setResizable(!1), b !== null && (clearTimeout(b), b = null);
  }, ie = s.isResizable();
  return ie || s.setResizable(!0), re(), {
    cancel: () => {
      b !== null && (clearTimeout(b), b = null), s.setBounds({
        x: H,
        y: q,
        width: e,
        height: t
      }), s.setResizable(ie);
    }
  };
}
function Ye(s) {
  return s < 0.5 ? 4 * s * s * s : 1 - Math.pow(-2 * s + 2, 3) / 2;
}
const te = N(u.getPath("userData"), "undetectability.enabled");
let F = me(te);
function Y() {
  return F;
}
function Ce() {
  F ? Qe() : Je();
}
function Je() {
  ye(te, ""), F = !0, l.createOrRecreateWindow();
}
function Qe() {
  try {
    be(te);
  } catch {
  }
  F = !1, l.createOrRecreateWindow();
}
class Ae {
  window;
  undetectabilityEnabled;
  currentDisplay = f.getPrimaryDisplay();
  constructor(e, t) {
    this.window = new ge(
      e || {
        show: C,
        // on Windows, we must show immediately for undetectability to work
        type: "panel",
        // window style options
        alwaysOnTop: !0,
        transparent: !0,
        frame: !1,
        roundedCorners: !1,
        hasShadow: !1,
        // window resize options
        fullscreenable: !1,
        minimizable: !1,
        // macOS specific options
        hiddenInMissionControl: !0,
        // Windows specific options
        skipTaskbar: t.undetectabilityEnabled,
        webPreferences: {
          preload: N(x, "../preload/index.cjs")
        }
      }
    ), this.undetectabilityEnabled = t.undetectabilityEnabled, this.undetectabilityEnabled && this.window.setContentProtection(!0), this.window.setVisibleOnAllWorkspaces(!0, { visibleOnFullScreen: !0 }), this.window.setResizable(!1), C && (this.window.setAlwaysOnTop(!0, "screen-saver", 1), this.window.webContents.setBackgroundThrottling(!1)), this.moveToPrimaryDisplay(), this.setIgnoreMouseEvents(!0), this.window.once("ready-to-show", () => {
      this.window.show();
    }), this.window.webContents.on("will-navigate", (n) => {
      n.preventDefault();
    }), this.window.webContents.setWindowOpenHandler((n) => {
      try {
        const r = new URL(n.url);
        (r.protocol === "https:" || m && r.protocol === "http:") && (X.openExternal(n.url), this.sendToWebContents("opened-external-link", null));
      } catch (r) {
        v(`error trying to open url ${n.url}`, r);
      }
      return { action: "deny" };
    }), m && process.env.ELECTRON_RENDERER_URL ? this.window.loadURL(process.env.ELECTRON_RENDERER_URL) : this.window.loadURL("app://renderer/index.html"), t?.finishedOnboarding && this.window.webContents.once("did-finish-load", () => {
      this.sendToWebContents("trigger-login", null);
    });
  }
  getCurrentDisplay() {
    return this.currentDisplay;
  }
  sendToWebContents(e, t) {
    this.window.isDestroyed() || this.window.webContents.send(e, t);
  }
  setIgnoreMouseEvents(e) {
    this.window.setIgnoreMouseEvents(e, { forward: !0 });
  }
  resizeWindow(e, t, n) {
    Xe(this.window, e, t, n);
  }
  focus() {
    this.window.focus();
  }
  blur() {
    C && (this.window.setFocusable(!1), this.window.setFocusable(!0), this.window.setSkipTaskbar(!0)), this.window.blur();
  }
  close() {
    this.window.isDestroyed() || this.window.close();
  }
  isDestroyed() {
    return this.window.isDestroyed();
  }
  getBounds() {
    return this.window.getBounds();
  }
  moveToPrimaryDisplay() {
    const e = f.getPrimaryDisplay();
    this.moveToDisplay(e);
  }
  moveToDisplay(e) {
    this.currentDisplay = e, this.window.setPosition(e.workArea.x, e.workArea.y), this.window.setSize(e.workArea.width, e.workArea.height), this.sendToWebContents("display-changed", null);
  }
  reload() {
    this.window.webContents.reload();
  }
  onUnload(e) {
    this.window.webContents.on("did-navigate", e);
  }
  toggleDevTools() {
    this.window.webContents.isDevToolsOpened() ? this.window.webContents.closeDevTools() : (this.window.webContents.openDevTools({ mode: "detach" }), u.focus());
  }
}
class ue extends Ae {
  constructor(e) {
    const t = {
      show: !0,
      alwaysOnTop: !1,
      transparent: !0,
      frame: !1,
      roundedCorners: !1,
      hasShadow: !0,
      fullscreenable: !1,
      minimizable: !1,
      hiddenInMissionControl: !1,
      skipTaskbar: !0,
      webPreferences: {
        preload: N(x, "../preload/index.cjs")
      }
    };
    super(t, e), super.moveToPrimaryDisplay();
  }
  setIgnoreMouseEvents(e) {
  }
  moveToPrimaryDisplay() {
    const e = f.getPrimaryDisplay();
    this.window.setPosition(e.workArea.x, e.workArea.y), this.window.center();
  }
}
class Ze {
  currentWindow = null;
  handlers = /* @__PURE__ */ new Set();
  handleDockIcon() {
    if (!I) return;
    const e = this.currentWindow instanceof ue;
    Y() && !e ? u.dock.hide() : u.dock.show();
  }
  createOrRecreateWindow(e) {
    const t = {
      undetectabilityEnabled: Y(),
      ...e
    };
    this.currentWindow && this.currentWindow.close(), this.currentWindow = ve() ? new Ae(void 0, t) : new ue(t);
    for (const n of this.handlers)
      n(this.currentWindow);
    return this.handleDockIcon(), this.currentWindow;
  }
  /** Can only be called after createWindow() */
  getCurrentWindow() {
    if (!this.currentWindow)
      throw new Error("No current window. Did you call createWindow()?");
    return this.currentWindow;
  }
  /** Runs the handlers immediately */
  onWindowChange(e) {
    return this.handlers.add(e), this.currentWindow && e(this.currentWindow), () => {
      this.handlers.delete(e);
    };
  }
}
const l = new Ze();
function Te() {
  const e = l.getCurrentWindow().getBounds(), t = f.getDisplayMatching(e);
  return f.getAllDisplays().map((n) => ({
    ...n,
    label: n.label || `Display ${n.id}`,
    primary: n.id === f.getPrimaryDisplay().id,
    current: n.id === t.id
  }));
}
function Ee(s) {
  return f.getAllDisplays().find((e) => e.id === s);
}
class Ke {
  window;
  displayId;
  constructor(e, t) {
    this.displayId = e.id, this.window = new ge({
      show: !1,
      frame: !1,
      transparent: !0,
      alwaysOnTop: !0,
      skipTaskbar: !0,
      resizable: !1,
      movable: !1,
      minimizable: !1,
      maximizable: !1,
      fullscreenable: !1,
      x: e.bounds.x,
      y: e.bounds.y,
      width: e.bounds.width,
      height: e.bounds.height,
      webPreferences: {
        preload: N(x, "../preload/index.cjs")
      }
    }), this.window.setVisibleOnAllWorkspaces(!0, { visibleOnFullScreen: !0 }), this.window.setIgnoreMouseEvents(!1);
    const n = () => {
      console.log(`[DisplayOverlay] Overlay click triggered for display ${this.displayId}`), t(this.displayId);
    }, r = `overlay-click-${this.displayId}`;
    U.on(r, n), this.window.on("closed", () => {
      console.log(`[DisplayOverlay] Cleaning up IPC handler for display ${this.displayId}`), U.removeListener(r, n);
    }), this.window.webContents.on("will-navigate", (i) => {
      i.preventDefault();
    }), this.window.webContents.setWindowOpenHandler(() => ({ action: "deny" })), this.loadReactOverlay(e, r);
  }
  loadReactOverlay(e, t) {
    const n = {
      display: {
        id: e.id,
        label: e.label || `Display ${e.id}`,
        bounds: e.bounds
      },
      ipcChannel: t,
      onOverlayClick: () => {
      }
      // This will be handled via IPC
    };
    let r;
    m && process.env.ELECTRON_RENDERER_URL ? r = new URL(`${process.env.ELECTRON_RENDERER_URL}/overlay.html`) : r = new URL("app://renderer/overlay.html");
    const i = encodeURIComponent(JSON.stringify(n));
    r.searchParams.set("displayData", i), this.window.loadURL(r.toString());
  }
  show() {
    this.window.show();
  }
  hide() {
    this.window.hide();
  }
  highlight() {
    this.window.webContents.executeJavaScript(`
      window.dispatchEvent(new CustomEvent('highlight'));
    `).catch(() => {
    });
  }
  unhighlight() {
    this.window.webContents.executeJavaScript(`
      window.dispatchEvent(new CustomEvent('unhighlight'));
    `).catch(() => {
    });
  }
  destroy() {
    console.log(`[DisplayOverlay] Destroying overlay for display ${this.displayId}`), this.window.isDestroyed() || this.window.close();
  }
  getBounds() {
    return this.window.getBounds();
  }
}
class et {
  overlays = /* @__PURE__ */ new Map();
  isActive = !1;
  showOverlays() {
    console.log("[DisplayOverlayManager] Showing overlays"), this.hideOverlays(), this.isActive = !0;
    const e = f.getAllDisplays(), t = l.getCurrentWindow(), n = t.getBounds(), r = f.getDisplayMatching(n);
    for (const i of e) {
      if (i.id === r.id)
        continue;
      const d = new Ke(i, (a) => {
        if (console.log(
          `[DisplayOverlayManager] Display ${a} clicked, checking if active: ${this.isActive}`
        ), !this.isActive) {
          console.log(
            `[DisplayOverlayManager] Ignoring click for display ${a} - overlays are inactive`
          );
          return;
        }
        console.log(`[DisplayOverlayManager] Moving window to display ${a}`);
        const h = Ee(a);
        h && t.moveToDisplay(h), this.hideOverlays();
      });
      this.overlays.set(i.id, d), d.show();
    }
  }
  hideOverlays() {
    console.log("[DisplayOverlayManager] Hiding overlays"), this.isActive = !1;
    for (const e of this.overlays.values())
      e.destroy();
    this.overlays.clear();
  }
  highlightDisplay(e) {
    const t = this.overlays.get(e);
    t && t.highlight();
  }
  unhighlightDisplay(e) {
    const t = this.overlays.get(e);
    t && t.unhighlight();
  }
}
const k = new et(), tt = "x-desktop-access-token";
let J = null;
function st(s) {
  J = s;
}
function nt() {
  W.defaultSession.webRequest.onBeforeSendHeaders((s, e) => {
    try {
      const t = new URL(s.url);
      J && t.origin === "https://app.cluely.com" && (s.requestHeaders[tt] = J);
    } catch {
    }
    e({ requestHeaders: s.requestHeaders });
  }), W.defaultSession.webRequest.onBeforeRequest((s, e) => {
    try {
      const t = new URL(s.url);
      ot(t) && l.getCurrentWindow().sendToWebContents("embedded-dashboard-non-desktop-route-redirect", null);
    } catch {
    }
    e({});
  }), W.defaultSession.webRequest.onBeforeRedirect((s) => {
    try {
      const e = new URL(s.referrer), t = new URL(s.redirectURL);
      pe(e) && !pe(t) && l.getCurrentWindow().sendToWebContents("embedded-dashboard-non-desktop-route-redirect", null);
    } catch {
    }
  });
}
function pe(s) {
  return s.origin === "https://app.cluely.com" && s.pathname.startsWith("/desktop/");
}
function ot(s) {
  return s.origin === "https://app.cluely.com" && s.pathname === "/login";
}
const rt = {
  "quit-app": o.null(),
  "check-for-update": o.null(),
  "install-update": o.null(),
  "get-updater-state": o.null(),
  "finish-onboarding": o.null(),
  "reset-onboarding": o.null(),
  "register-global-shortcut": o.object({
    accelerator: o.string()
  }),
  "unregister-global-shortcut": o.object({
    accelerator: o.string()
  }),
  "enable-dev-shortcuts": o.null(),
  "reset-global-shortcuts": o.null(),
  "set-ignore-mouse-events": o.object({
    ignore: o.boolean()
  }),
  "resize-window": o.object({
    width: o.number(),
    height: o.number(),
    duration: o.number()
  }),
  "focus-window": o.null(),
  "unfocus-window": o.null(),
  // Display management events
  "get-available-displays": o.null(),
  "get-invisible": o.null(),
  "move-window-to-display": o.object({
    displayId: o.number()
  }),
  "show-display-overlays": o.null(),
  "hide-display-overlays": o.null(),
  "highlight-display": o.object({
    displayId: o.number()
  }),
  "unhighlight-display": o.object({
    displayId: o.number()
  }),
  "set-embedded-dashboard-access-token": o.object({
    accessToken: o.string().nullable()
  }),
  // Mac specific events
  "mac-open-system-settings": o.object({
    section: o.enum(["privacy > microphone", "privacy > screen-recording"])
  }),
  "mac-set-native-recorder-enabled": o.object({
    enabled: o.boolean(),
    useV2: o.boolean().optional()
  }),
  "mac-set-mic-monitor-enabled": o.object({
    enabled: o.boolean()
  }),
  "toggle-invisible": o.null(),
  "logout-user": o.null(),
  "login-user": o.object({
    userId: o.string()
  }),
  "set-auto-launch-enabled": o.object({
    enabled: o.boolean()
  })
}, it = {
  "request-has-onboarded": {
    payload: o.null(),
    response: o.object({
      hasOnboarded: o.boolean()
    })
  },
  "request-media-permission": {
    payload: o.enum(["microphone", "camera", "screen"]),
    response: o.boolean()
  },
  "capture-screenshot": {
    payload: o.null(),
    response: o.object({
      contentType: o.string(),
      data: o.instanceof(Buffer)
    })
  },
  "mac-check-macos-version": {
    payload: o.null(),
    response: o.object({
      isSupported: o.boolean()
    })
  },
  "get-auto-launch-enabled": {
    payload: o.null(),
    response: o.object({
      enabled: o.boolean()
    })
  }
};
function c(s, e) {
  const t = rt[s], n = (r, i) => {
    const d = t.parse(i);
    e(r, d);
  };
  U.on(s, n);
}
function O(s, e) {
  const t = it[s].payload, n = (r, i) => {
    const d = t.parse(i);
    return e(r, d);
  };
  U.handle(s, n);
}
async function Oe(s, e, t) {
  const n = _.basename(e);
  return await new Promise((r, i) => {
    let d = "";
    s.stdout.on("data", (a) => {
      t.recordStdout && (d += a.toString());
    }), s.stderr.on("data", (a) => {
      v(`[${n}] stderr: ${a}`);
    }), s.on("close", (a) => {
      a !== 0 ? (v(`[${n}] process exited with code ${a}`), i(new Error(`Process exited with code ${a}`))) : r({ stdout: d });
    }), s.on("error", (a) => {
      v(`[${n}] process error: ${a}`), i(a);
    });
  });
}
const at = 13, ct = 24e3;
async function lt() {
  const s = Z("sw_vers", ["-productVersion"]), { stdout: e } = await Oe(s, "sw_vers", { recordStdout: !0 }), t = Number.parseInt(e.split(".")[0] ?? "", 10);
  return { isSupported: !Number.isNaN(t) && t >= at };
}
const dt = _.join(
  // app.getAppPath(): root folder of the electron app
  // process.resourcesPath: the Resources folder in the app's package contents
  m ? u.getAppPath() : process.resourcesPath,
  "macExtraResources"
);
function De(s) {
  return _.join(dt, s);
}
const he = De("nativeMacRecorder"), ut = De("nativeMacRecorder_v2");
let R = null;
u.on("before-quit", () => se());
function pt(s, e) {
  se(), R = Z(e ? ut : he, [ct.toString()]), ht(R, s, e), Oe(R, he, { recordStdout: !1 });
}
function se() {
  R?.kill("SIGINT"), R = null;
}
function ht(s, e, t) {
  let n = "";
  s.stdout.on("data", (r) => {
    const i = r.toString(), a = (n + i).split(`
`);
    if (n = a.pop() ?? "", t) {
      const h = {};
      for (const p of a) {
        const g = p.indexOf(":");
        if (g === -1) continue;
        const S = p.slice(0, g), T = p.slice(g + 1);
        h[S] ??= [], h[S].push(T);
      }
      for (const p of ["mic", "system"]) {
        const g = h[p];
        g && e.sendToWebContents("mac-native-recorder-data", {
          source: p,
          base64Data: g.join("")
        });
      }
    } else
      e.sendToWebContents("mac-native-recorder-data", {
        source: "system",
        base64Data: a.join("")
        // concatenate all complete lines
      });
  });
}
const ft = [
  ["Google Chrome", "Google Chrome"],
  ["firefox", "Mozilla Firefox"],
  ["com.apple.WebKit", "Safari"],
  ["Arc", "Arc Browser"],
  ["Arc Browser", "Arc Browser"],
  // Alternative process name
  ["Arc.app", "Arc Browser"],
  // App bundle name
  ["Microsoft Edge", "Microsoft Edge"],
  ["zoom.us", "Zoom"],
  ["GoogleMeet", "Google Meet"],
  // TODO: need to test
  ["Slack", "Slack"],
  ["Teams", "Microsoft Teams"],
  // Lower priority apps
  ["Brave Browser", "Brave Browser"],
  ["Brave", "Brave Browser"],
  // Alternative process name
  ["Brave.app", "Brave Browser"],
  // App bundle name
  ["Opera", "Opera Browser"],
  ["Opera Browser", "Opera Browser"],
  // Alternative process name
  ["Opera.app", "Opera Browser"],
  // App bundle name
  ["Vivaldi", "Vivaldi Browser"],
  ["Vivaldi Browser", "Vivaldi Browser"],
  // Alternative process name
  ["Vivaldi.app", "Vivaldi Browser"],
  // App bundle name
  ["Comet", "Comet Browser"],
  ["Comet Browser", "Comet Browser"],
  // Alternative process name
  ["Comet.app", "Comet Browser"],
  // App bundle name
  ["Dia", "Dia Browser"],
  ["Dia Browser", "Dia Browser"],
  // Alternative process name
  ["Dia.app", "Dia Browser"],
  // App bundle name
  ["Fellou", "Fellou AI Browser"],
  ["Fellou AI", "Fellou AI Browser"],
  // Alternative process name
  ["Fellou.app", "Fellou AI Browser"],
  // App bundle name
  ["VoiceMemos", "Voice Memos"],
  ["FaceTime", "FaceTime"],
  // TODO: need to test
  ["Discord", "Discord"],
  // TODO: need to test
  ["QuickTimePlayer", "QuickTime Player"]
  // TODO: need to test
], gt = [
  ["company.thebrowser.browser.helper", "Arc Browser"],
  ["com.brave.Browser.helper", "Brave Browser"],
  ["com.microsoft.edgemac.helper", "Microsoft Edge"],
  ["com.operasoftware.Opera.helper", "Opera Browser"],
  ["com.vivaldi.Vivaldi.helper", "Vivaldi Browser"],
  ["com.google.Chrome.helper", "Google Chrome"],
  ["org.mozilla.firefox.helper", "Mozilla Firefox"],
  ["com.cometbrowser.Comet.helper", "Comet Browser"],
  ["com.diabrowser.Dia.helper", "Dia Browser"],
  ["com.fellou.browser.helper", "Fellou AI Browser"]
];
function wt(s) {
  for (const [e, t] of ft)
    if (s.includes(e))
      return t;
  for (const [e, t] of gt)
    if (s.includes(e))
      return t;
  return null;
}
class w extends $e {
  proc = null;
  // Ultra-aggressive frequency deduplication
  patternFrequencyCounter = /* @__PURE__ */ new Map();
  FREQUENCY_WINDOW_MS = 5e3;
  // 5 second window (ultra-aggressive)
  HIGH_FREQ_THRESHOLD = 1;
  // 1 line per 5 seconds (ultra-restrictive)
  // Ultra-aggressive batch processing
  lineBuffer = [];
  BATCH_SIZE = 100;
  // Much larger batch size (ultra-aggressive)
  BATCH_TIMEOUT_MS = 1e3;
  // Much longer timeout (ultra-aggressive)
  batchTimeout = null;
  // Ultra-aggressive global rate limiting
  lastProcessTime = 0;
  MIN_PROCESS_INTERVAL_MS = 2e3;
  // Minimum 2 seconds between processing batches
  // Pre-compiled regex patterns for better performance
  static SESSION_NAME_REGEX = /"session":\{[^}]*"name":"([A-Za-z0-9_. ]+)\(\d+\)".*?"input_running":\s*(true|false)/;
  static AVCAPTURE_USED_REGEX = /AVCaptureDevice was used for audio by "(.*?)"/;
  static AVCAPTURE_STOPPED_REGEX = /AVCaptureDevice was stopped being used for audio by "(.*?)"/;
  static BUNDLE_ID_REGEX = /BundleID\s*=\s*([A-Za-z0-9_.]+)/;
  matchRules = [
    {
      type: "mic-used",
      subsystem: "com.apple.coreaudio:as_server",
      matchSubstring: '\\"input_running\\":true',
      regex: w.SESSION_NAME_REGEX
    },
    {
      type: "mic-off",
      subsystem: "com.apple.coreaudio:as_server",
      matchSubstring: '\\"input_running\\":false',
      regex: w.SESSION_NAME_REGEX
    },
    {
      type: "mic-used",
      subsystem: "com.apple.audio.AVFAudio",
      matchSubstring: "AVCaptureDevice was used",
      regex: w.AVCAPTURE_USED_REGEX
    },
    {
      type: "mic-off",
      subsystem: "com.apple.audio.AVFAudio",
      matchSubstring: "AVCaptureDevice was stopped",
      regex: w.AVCAPTURE_STOPPED_REGEX
    },
    {
      type: "mic-used",
      subsystem: "com.apple.audio.ASDT",
      matchSubstring: "startStream: running state: 1"
    },
    {
      type: "mic-off",
      subsystem: "com.apple.audio.ASDT",
      matchSubstring: "stopStream: running state: 0"
    },
    // Firefox-specific rules for AUHAL subsystem
    {
      type: "mic-used",
      subsystem: "com.apple.coreaudio:AUHAL",
      matchSubstring: "connecting device"
    },
    {
      type: "mic-off",
      subsystem: "com.apple.coreaudio:AUHAL",
      matchSubstring: "nothing to teardown"
    },
    // Firefox AVCapture rules - only for specific patterns, not general coremedia
    {
      type: "mic-used",
      subsystem: "com.apple.coremedia",
      matchSubstring: "logging capture stack initiator"
    },
    // Bundle ID patterns for more accurate browser detection - only when actually using mic
    {
      type: "mic-used",
      matchSubstring: "BundleID",
      regex: w.BUNDLE_ID_REGEX
    },
    {
      type: "mic-off",
      matchSubstring: "BundleID",
      regex: w.BUNDLE_ID_REGEX
    }
  ];
  start() {
    if (this.proc) return;
    console.log("[MicMonitor] start() called");
    const t = ["stream", "--info", "--predicate", this.buildPredicate(), "--style", "compact"], n = Z("log", t);
    this.proc = n, this.proc.stdout.on("data", (r) => {
      const i = r.toString();
      if (i.includes("Filtering the log data using") || i.includes("log stream"))
        return;
      const d = i.split(`
`);
      for (const a of d)
        a && a.length > 0 && this.lineBuffer.push(a);
      this.lineBuffer.length >= this.BATCH_SIZE ? this.processBatch() : this.batchTimeout || (this.batchTimeout = setTimeout(() => {
        this.processBatch();
      }, this.BATCH_TIMEOUT_MS));
    }), this.proc.stderr.on("data", (r) => {
      v("[MicMonitor stderr]", r.toString());
    }), this.proc.on("exit", (r) => {
      console.log(`[MicMonitor] exited with code ${r}`), this.proc === n && (this.proc = null);
    });
  }
  buildPredicate() {
    const e = [];
    for (const r of this.matchRules) {
      let i = `eventMessage CONTAINS "${r.matchSubstring}"`;
      r.subsystem && (i = `(subsystem CONTAINS "${r.subsystem.split(":")[0]}" AND ${i})`), e.push(`(${i})`);
    }
    return `(${e.join(" || ")} || (subsystem CONTAINS "com.apple.coremedia" AND eventMessage CONTAINS "logging capture stack initiator")) AND (process CONTAINS "audio" OR process CONTAINS "coreaudio" OR process CONTAINS "AVFAudio" OR process CONTAINS "ASDT" OR process CONTAINS "AUHAL") AND NOT (eventMessage CONTAINS "debug" OR eventMessage CONTAINS "DEBUG" OR eventMessage CONTAINS "info" OR eventMessage CONTAINS "INFO" OR eventMessage CONTAINS "display" OR eventMessage CONTAINS "screen" OR eventMessage CONTAINS "loopback" OR eventMessage CONTAINS "getDisplayMedia" OR eventMessage CONTAINS "DesktopCapture" OR eventMessage CONTAINS "ScreenCapture" OR eventMessage CONTAINS "system_audio" OR eventMessage CONTAINS "system-audio" OR eventMessage CONTAINS "displaySurface" OR eventMessage CONTAINS "monitor" OR eventMessage CONTAINS "window")`;
  }
  processBatch() {
    if (this.batchTimeout && (clearTimeout(this.batchTimeout), this.batchTimeout = null), this.lineBuffer.length === 0) return;
    const e = Date.now();
    if (e - this.lastProcessTime < this.MIN_PROCESS_INTERVAL_MS) {
      this.batchTimeout = setTimeout(() => this.processBatch(), this.MIN_PROCESS_INTERVAL_MS);
      return;
    }
    this.lastProcessTime = e;
    const t = this.lineBuffer.splice(0, this.BATCH_SIZE);
    for (const n of t)
      this.processLine(n, e);
    this.cleanOldFrequencyCounters(e);
  }
  processLine(e, t) {
    if (!(e.includes("input_running") || e.includes("AVCaptureDevice") || e.includes("startStream") || e.includes("stopStream") || e.includes("connecting device") || e.includes("nothing to teardown") || e.includes("logging capture stack initiator") || e.includes("BundleID")) || e.includes("display") || e.includes("screen") || e.includes("loopback") || e.includes("getDisplayMedia") || e.includes("DesktopCapture") || e.includes("ScreenCapture") || e.includes("system_audio") || e.includes("system-audio") || e.includes("displaySurface") || e.includes("monitor") || e.includes("window") || e.includes("terminated") || e.includes("exited") || e.includes("cleanup") || e.includes("dealloc") || e.includes("destroy") || e.includes("shutdown") || e.includes("quit") || e.includes("close") || e.includes("disconnect") || e.includes("unload") || e.includes("teardown") || e.includes("release") || e.includes("finalize") || e.includes("session ended") || e.includes("meeting ended") || e.includes("call ended") || e.includes("hang up") || e.includes("leave meeting") || e.includes("leave call") || e.includes("endInterruption") || e.includes("going inactive") || e.includes("Category = MediaPlayback") || e.includes("Recording = NO") || e.includes('input_running":false') || e.includes("Active = NO") || e.includes("requestForSharedOwnership") || e.includes("stop") || // Filter out lines with empty deviceUIDs ONLY when they're part of cleanup events
    // (when input_running is true but we have cleanup indicators)
    e.includes('input_running":true') && e.includes('"deviceUIDs":[]') && (e.includes("Recording = NO") || e.includes("Active = NO") || e.includes("endInterruption") || e.includes("going inactive") || e.includes("stopStream")))
      return;
    const n = this.extractPattern(e), r = this.patternFrequencyCounter.get(n);
    if (r) {
      if (r.count++, r.count > this.HIGH_FREQ_THRESHOLD && t - r.firstSeen < this.FREQUENCY_WINDOW_MS)
        return;
    } else
      this.patternFrequencyCounter.set(n, { count: 1, firstSeen: t });
    for (const i of this.matchRules) {
      const d = i.matchSubstring.replace(/\\"/g, '"');
      if (e.includes(d)) {
        let a = "";
        if (i.regex) {
          const p = i.regex.exec(e);
          p?.[1] && (i.regex === w.SESSION_NAME_REGEX ? (p[2] === "true" && i.type === "mic-used" || p[2] === "false" && i.type === "mic-off") && (a = p[1]) : a = p[1]);
        }
        if (!a) continue;
        const h = wt(a);
        if (!h)
          break;
        if (i.matchSubstring === "BundleID" && (e.includes("endInterruption") || e.includes("going inactive") || e.includes('input_running":false') || e.includes("Active = NO") || e.includes("Category = MediaPlayback") || !(e.includes('"input_running":true') || e.includes("Recording = YES") && e.includes("Active = YES"))))
          continue;
        console.log(`[MicMonitor] DEBUG: Matched rule: ${i.type} for app: ${h}`), this.emit(i.type, { app: h, message: e });
        return;
      }
    }
  }
  extractPattern(e) {
    for (const t of this.matchRules) {
      const n = t.matchSubstring.replace(/\\"/g, '"');
      if (e.includes(n)) {
        let r = "";
        if (t.regex) {
          const a = t.regex.exec(e);
          a?.[1] && (r = a[1]);
        }
        const i = t.subsystem?.split(":")[0] || "generic", d = t.type === "mic-used" ? "used" : "off";
        return r ? `${d}_${i}_${r}` : `${d}_${i}_${n.replace(/[^a-zA-Z0-9]/g, "_")}`;
      }
    }
    return e.replace(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}/g, "T").replace(/\[\d+:\d+\]/g, "[P]").replace(/\(\d+\)/g, "(I)");
  }
  cleanOldFrequencyCounters(e) {
    for (const [t, n] of this.patternFrequencyCounter.entries())
      e - n.firstSeen > this.FREQUENCY_WINDOW_MS && this.patternFrequencyCounter.delete(t);
  }
  stop() {
    this.batchTimeout && (clearTimeout(this.batchTimeout), this.batchTimeout = null), this.proc && (this.proc.kill(), this.proc = null);
  }
}
let y = null;
u.on("before-quit", () => Re());
function mt(s) {
  y || I && (y = new w(), y.start(), y.on("mic-used", (e) => {
    s.sendToWebContents("unhide-window", null), s.sendToWebContents("mic-used", e);
  }), y.on("mic-off", (e) => {
    s.sendToWebContents("mic-off", e);
  }));
}
function Re() {
  y && (y.stop(), y = null);
}
async function yt() {
  const s = l.getCurrentWindow().getCurrentDisplay();
  try {
    const t = (await Q.getSources({
      types: ["screen"],
      thumbnailSize: {
        width: s.bounds.width,
        height: s.bounds.height
      }
    })).find((n) => n.display_id === s.id.toString());
    if (!t)
      throw new Error("Unable to capture screenshot: no display source found");
    return { data: t.thumbnail.toPNG(), contentType: "image/png" };
  } catch (e) {
    throw I && Se(), e;
  }
}
let Me = m;
const A = /* @__PURE__ */ new Set();
function bt(s) {
  if (A.has(s)) {
    console.warn(`Shortcut already registered: ${s}`);
    return;
  }
  A.add(s), $();
}
function vt(s) {
  if (!A.has(s)) {
    console.warn(`Shortcut not registered: ${s}`);
    return;
  }
  A.delete(s), $();
}
function St() {
  Me = !0, $();
}
function Ct() {
  A.clear(), $();
}
function $() {
  E.unregisterAll();
  for (const s of A)
    E.register(s, () => {
      l.getCurrentWindow().sendToWebContents("global-shortcut-triggered", { accelerator: s });
    }) || v(`Failed to register global shortcut: ${s}`);
  Me && (E.register("CommandOrControl+Alt+Shift+I", () => {
    Ce();
  }), E.register("CommandOrControl+Alt+R", () => {
    l.getCurrentWindow().reload();
  }), E.register("CommandOrControl+Alt+I", () => {
    l.getCurrentWindow().toggleDevTools();
  }));
}
const { autoUpdater: M } = He;
let D = "none";
function At() {
  M.on("update-available", () => {
    D = "available", l.getCurrentWindow().sendToWebContents("updater-state", { state: D });
  }), M.on("update-downloaded", () => {
    D = "downloaded", l.getCurrentWindow().sendToWebContents("updater-state", { state: D });
  }), M.checkForUpdatesAndNotify();
}
function Tt() {
  return D;
}
function Et() {
  let s = l.getCurrentWindow();
  l.onWindowChange((e) => {
    s = e;
  }), c("quit-app", () => {
    u.quit();
  }), c("check-for-update", () => {
    M.checkForUpdatesAndNotify();
  }), c("install-update", () => {
    M.quitAndInstall();
  }), c("get-updater-state", () => {
    s.sendToWebContents("updater-state", { state: Tt() });
  }), O("request-has-onboarded", async () => ({ hasOnboarded: ve() })), O("request-media-permission", async (e, t) => {
    if (process.platform === "darwin") {
      if (t === "screen")
        try {
          return await Q.getSources({ types: ["screen"] }), !0;
        } catch {
          return !1;
        }
      try {
        const n = ce.getMediaAccessStatus(t);
        return n === "not-determined" ? await ce.askForMediaAccess(t) : n === "granted";
      } catch (n) {
        return v("Media permission error:", n), !1;
      }
    }
    return !0;
  }), c("finish-onboarding", () => {
    Ge();
  }), c("reset-onboarding", () => {
    Se();
  }), O("get-auto-launch-enabled", async () => ({ enabled: u.getLoginItemSettings().openAtLogin })), c("set-auto-launch-enabled", (e, { enabled: t }) => {
    m || u.setLoginItemSettings({
      openAtLogin: t,
      openAsHidden: !1
      // Always show the app when auto-launching
    });
  }), c("register-global-shortcut", (e, { accelerator: t }) => {
    bt(t);
  }), c("unregister-global-shortcut", (e, { accelerator: t }) => {
    vt(t);
  }), c("enable-dev-shortcuts", () => {
    St();
  }), c("reset-global-shortcuts", () => {
    Ct();
  }), c("set-ignore-mouse-events", (e, { ignore: t }) => {
    s.setIgnoreMouseEvents(t);
  }), c("resize-window", (e, { width: t, height: n, duration: r }) => {
    s.resizeWindow(t, n, r);
  }), c("focus-window", () => {
    s.focus();
  }), c("unfocus-window", () => {
    s.blur();
  }), O("capture-screenshot", async () => {
    const { contentType: e, data: t } = await yt();
    return { contentType: e, data: t };
  }), c("get-available-displays", () => {
    const e = Te();
    s.sendToWebContents("available-displays", { displays: e });
  }), c("get-invisible", () => {
    s.sendToWebContents("invisible-changed", {
      invisible: Y()
    });
  }), c("move-window-to-display", (e, { displayId: t }) => {
    const n = Ee(t);
    n && s.moveToDisplay(n);
  }), c("show-display-overlays", () => {
    k.showOverlays();
  }), c("hide-display-overlays", () => {
    console.log("[IPC] hide-display-overlays called"), k.hideOverlays();
  }), c("highlight-display", (e, { displayId: t }) => {
    k.highlightDisplay(t);
  }), c("unhighlight-display", (e, { displayId: t }) => {
    k.unhighlightDisplay(t);
  }), c("toggle-invisible", (e) => {
    Ce();
  }), c("login-user", (e, { userId: t }) => {
    je(t);
  }), c("logout-user", (e) => {
    ze();
  }), c("set-embedded-dashboard-access-token", (e, { accessToken: t }) => {
    st(t);
  }), I && (O("mac-check-macos-version", async () => {
    const { isSupported: e } = await lt();
    return { isSupported: e };
  }), c("mac-open-system-settings", (e, { section: t }) => {
    t === "privacy > microphone" && X.openExternal(
      "x-apple.systempreferences:com.apple.preference.security?Privacy_Microphone"
    ), t === "privacy > screen-recording" && X.openExternal(
      "x-apple.systempreferences:com.apple.preference.security?Privacy_ScreenCapture"
    );
  }), c("mac-set-native-recorder-enabled", (e, { enabled: t, useV2: n }) => {
    t ? pt(s, n ?? !1) : se();
  }), c("mac-set-mic-monitor-enabled", (e, { enabled: t }) => {
    t ? mt(s) : Re();
  }));
}
function Ot() {
  const s = () => {
    l.getCurrentWindow().moveToPrimaryDisplay();
    const e = Te();
    l.getCurrentWindow().sendToWebContents("available-displays", { displays: e });
  };
  f.on("display-added", s), f.on("display-removed", s), f.on("display-metrics-changed", s);
}
function Dt() {
  m || we.handle("app", async (s) => await (async () => {
    const { host: n, pathname: r } = new URL(s.url);
    if (n !== "renderer" || r.includes("..")) return null;
    const i = _.join(_.resolve(x, "../renderer"), r);
    return Ue.fetch(qe(i).toString());
  })() || new Response("bad", {
    status: 400,
    headers: { "content-type": "text/html" }
  }));
}
const _e = "cluely";
function Rt() {
  de.on("request", (s) => {
    l.handleDockIcon();
    const e = l.getCurrentWindow();
    e.sendToWebContents("unhide-window", null), Ne(s, e);
  }), de.initialize({
    protocol: _e,
    mode: m ? "development" : "production"
  }), fe(l.getCurrentWindow(), process.argv), u.on("second-instance", (s, e) => {
    const t = l.getCurrentWindow();
    t.sendToWebContents("unhide-window", null), fe(t, e);
  }), u.on("activate", () => {
    l.handleDockIcon(), l.getCurrentWindow().sendToWebContents("unhide-window", null);
  });
}
function fe(s, e) {
  const t = e.find((n) => n.startsWith(`${_e}://`));
  t && Ne(t, s);
}
function Ne(s, e) {
  const t = new URL(s), n = t.hostname, r = Object.fromEntries(t.searchParams);
  e.sendToWebContents("protocol-data", { route: n, params: r });
}
function Mt() {
  const s = le.buildFromTemplate([
    // preserve Cmd+C, Cmd+V, etc.
    { role: "editMenu" }
  ]);
  le.setApplicationMenu(s);
}
function _t() {
  W.defaultSession.setDisplayMediaRequestHandler(
    (s, e) => {
      Q.getSources({ types: ["screen"] }).then((t) => {
        e({ video: t[0], audio: "loopback" });
      }).catch(() => {
        e({});
      });
    },
    // always use our custom handler
    { useSystemPicker: !1 }
  );
}
const Nt = "cluely";
I && u.dock.hide();
C && (u.requestSingleInstanceLock() || (u.quit(), process.exit(0)));
m || we.registerSchemesAsPrivileged([
  {
    scheme: "app",
    privileges: {
      standard: !0,
      secure: !0,
      supportFetchAPI: !0
    }
  }
]);
async function It() {
  C && u.disableHardwareAcceleration(), await u.whenReady(), Fe.setAppUserModelId(`com.${Nt}`), Mt(), _t(), Dt(), nt(), l.createOrRecreateWindow(), At(), Et(), Ot(), Rt();
}
It();
