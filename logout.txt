
> secure-kernel@1.1.2 dev
> cross-env NODE_ENV=development vite

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m

  VITE v5.4.19  ready in 168 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
  ➜  press h + enter to show help
vite v5.4.19 building for development...

watching for file changes...
vite v5.4.19 building for development...

watching for file changes...

build started...

build started...
transforming...
transforming...
✓ 1 modules transformed.
rendering chunks...
computing gzip size...
dist-electron/preload.js  14.49 kB │ gzip: 2.37 kB
built in 74ms.
✓ 771 modules transformed.
[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/store.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/MouseTrackingHelper.ts, /Users/<USER>/Desktop/coder-master/electron/WindowHelper.ts, /Users/<USER>/Desktop/coder-master/electron/handlers/MicrophoneASRManager.ts, /Users/<USER>/Desktop/coder-master/electron/handlers/SystemASRManager.ts, /Users/<USER>/Desktop/coder-master/electron/handlers/problemHandler.ts, /Users/<USER>/Desktop/coder-master/electron/ipcHandlers.ts, dynamic import will not move module into another chunk.

[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/handlers/UnifiedHistoryManager.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/handlers/MicrophoneASRManager.ts, /Users/<USER>/Desktop/coder-master/electron/handlers/SystemASRManager.ts, dynamic import will not move module into another chunk.

[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/handlers/MicrophoneASRManager.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/ipc/voiceHandlers.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/handlers/MicrophoneManager.ts, dynamic import will not move module into another chunk.

[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/handlers/MicrophoneManager.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/ipc/microphoneHandlers.ts, /Users/<USER>/Desktop/coder-master/electron/ipc/voiceHandlers.ts, dynamic import will not move module into another chunk.

[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/handlers/SystemASRManager.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/ipc/voiceHandlers.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/handlers/SystemAudioManager.ts, dynamic import will not move module into another chunk.

[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/handlers/SystemAudioManager.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/ipc/systemAudioHandlers.ts, /Users/<USER>/Desktop/coder-master/electron/ipc/voiceHandlers.ts, dynamic import will not move module into another chunk.

[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/handlers/problemHandler.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/ProcessingHelper.ts, /Users/<USER>/Desktop/coder-master/electron/ipc/voiceHandlers.ts, dynamic import will not move module into another chunk.

rendering chunks...
computing gzip size...
dist-electron/main.js  1,231.74 kB │ gzip: 254.12 kB
built in 1470ms.
从配置中读取模型列表: [
  'doubao-pro',
  'doubao-thinking',
  'deepseek-v3',
  'deepseek-r1',
  'gemini-2.5',
  'claude',
  'grok-4'
]
UnifiedHistoryManager: 实例已创建
WebSocket module loaded successfully for MicrophoneASR
MicrophoneASRManager instance created
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
MicrophoneASRManager: 语音配置加载成功 {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
MicrophoneManager: 初始化麦克风管理器
WebSocket module loaded successfully for SystemASR
SystemASRManager instance created
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
SystemASRManager: 语音配置加载成功 {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
Setting up app.whenReady() handler...
Main.ts loaded, app state: { isReady: false, version: '29.4.6' }
App 'ready' event fired
Electron app is ready, starting initialization...
initializeApp: Starting app initialization...
initializeApp: Creating app state...
所有快捷键已注销
快捷键 Alt+C 注册成功，已阻止事件传播
快捷键 Alt+V 注册成功，已阻止事件传播
快捷键 Alt+A 注册成功，已阻止事件传播
快捷键 CommandOrControl+Left 注册成功，已阻止事件传播
快捷键 CommandOrControl+Right 注册成功，已阻止事件传播
快捷键 CommandOrControl+Down 注册成功，已阻止事件传播
快捷键 CommandOrControl+Up 注册成功，已阻止事件传播
快捷键 CommandOrControl+B 注册成功，已阻止事件传播
快捷键 Alt+Z 注册成功，已阻止事件传播
快捷键 Alt+2 注册成功，已阻止事件传播
快捷键 Alt+3 注册成功，已阻止事件传播
快捷键 Alt+1 注册成功，已阻止事件传播
快捷键 CommandOrControl+Q 注册成功，已阻止事件传播
所有快捷键注册成功
键盘钩子已激活
设置 macOS 键盘钩子
注册系统级键盘监听器
添加键盘钩子: Alt+C
添加键盘钩子: Alt+Z
添加键盘钩子: Alt+1
添加键盘钩子: Alt+2
添加键盘钩子: Alt+3
添加键盘钩子: Alt+V
添加键盘钩子: Alt+A
添加键盘钩子: CommandOrControl+R
添加键盘钩子: CommandOrControl+Left
添加键盘钩子: CommandOrControl+Right
添加键盘钩子: CommandOrControl+Down
添加键盘钩子: CommandOrControl+Up
添加键盘钩子: CommandOrControl+B
添加键盘钩子: CommandOrControl+Q
系统级键盘拦截已设置
ShortcutsHelper initialized
鼠标点击事件监听设置完成
ioHook started
initializeApp: Creating main window...
初始化SystemAudioManager...
初始化MicrophoneManager...
初始化UnifiedHistoryManager...
注册语音处理IPC处理器...
注册语音助手 IPC 处理程序...
initializeApp: Initializing IPC handlers...
initializeApp: 注册全局快捷键...
重新注册全局快捷键
快捷键 Alt+C 已注销
快捷键 Alt+V 已注销
快捷键 Alt+A 已注销
快捷键 CommandOrControl+Left 已注销
快捷键 CommandOrControl+Right 已注销
快捷键 CommandOrControl+Down 已注销
快捷键 CommandOrControl+Up 已注销
快捷键 CommandOrControl+B 已注销
快捷键 Alt+Z 已注销
快捷键 Alt+2 已注销
快捷键 Alt+3 已注销
快捷键 Alt+1 已注销
快捷键 CommandOrControl+Q 已注销
所有快捷键已注销
快捷键 Alt+C 注册成功，已阻止事件传播
快捷键 Alt+V 注册成功，已阻止事件传播
快捷键 Alt+A 注册成功，已阻止事件传播
快捷键 CommandOrControl+Left 注册成功，已阻止事件传播
快捷键 CommandOrControl+Right 注册成功，已阻止事件传播
快捷键 CommandOrControl+Down 注册成功，已阻止事件传播
快捷键 CommandOrControl+Up 注册成功，已阻止事件传播
快捷键 CommandOrControl+B 注册成功，已阻止事件传播
快捷键 Alt+Z 注册成功，已阻止事件传播
快捷键 Alt+2 注册成功，已阻止事件传播
快捷键 Alt+3 注册成功，已阻止事件传播
快捷键 Alt+1 注册成功，已阻止事件传播
快捷键 CommandOrControl+Q 注册成功，已阻止事件传播
所有快捷键注册成功
initializeApp: 等待窗口加载完成后检查配置...
initializeApp: 窗口正在加载，等待加载完成...
UnifiedHistoryManager: 🧹 设置新主窗口，清空历史记录
UnifiedHistoryManager: 🚀 发送统一历史更新，共 0 条记录 {}
UnifiedHistoryManager: 历史记录已清空
SystemAudioManager: 主窗口已设置
注册系统音频IPC处理器...
UnifiedHistoryManager: 🧹 设置新主窗口，清空历史记录
UnifiedHistoryManager: 🚀 发送统一历史更新，共 0 条记录 {}
UnifiedHistoryManager: 历史记录已清空
SystemAudioManager: 主窗口已设置
移除已存在的system-audio:start-capturing处理器
移除已存在的system-audio:stop-capturing处理器
移除已存在的system-audio:get-status处理器
系统音频相关的IPC处理器已注册
UnifiedHistoryManager: 🧹 设置新主窗口，清空历史记录
UnifiedHistoryManager: 🚀 发送统一历史更新，共 0 条记录 {}
UnifiedHistoryManager: 历史记录已清空
MicrophoneManager: 主窗口已设置
注册麦克风IPC处理器...
UnifiedHistoryManager: 🧹 设置新主窗口，清空历史记录
UnifiedHistoryManager: 🚀 发送统一历史更新，共 0 条记录 {}
UnifiedHistoryManager: 历史记录已清空
MicrophoneManager: 主窗口已设置
移除已存在的microphone:start-capturing处理器
移除已存在的microphone:stop-capturing处理器
移除已存在的microphone:get-status处理器
移除已存在的microphone:process-audio处理器
麦克风相关的IPC处理器已注册
UnifiedHistoryManager: 🧹 设置新主窗口，清空历史记录
UnifiedHistoryManager: 🚀 发送统一历史更新，共 0 条记录 {}
UnifiedHistoryManager: 历史记录已清空
UnifiedHistoryManager 已初始化并设置主窗口
2025-07-24 18:34:45.395 Electron[33884:40583654] +[IMKClient subclass]: chose IMKClient_Modern
2025-07-24 18:34:45.395 Electron[33884:40583654] +[IMKInputSession subclass]: chose IMKInputSession_Modern
initializeApp: 窗口加载完成，开始配置检查
使用默认编程语言列表: [
  'Java',    'Python',
  'Python3', 'JavaScript',
  'C++',     'C',
  'Go',      'SQL',
  'Ruby',    'TypeScript',
  'Kotlin',  'Swift',
  'Rust'
]
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3',
      'deepseek-r1',
      'gemini-2.5',
      'claude',
      'grok-4'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
使用默认编程语言列表: [
  'Java',    'Python',
  'Python3', 'JavaScript',
  'C++',     'C',
  'Go',      'SQL',
  'Ruby',    'TypeScript',
  'Kotlin',  'Swift',
  'Rust'
]
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3',
      'deepseek-r1',
      'gemini-2.5',
      'claude',
      'grok-4'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
工具条位置已更新: { x: 16, y: 12, width: 518, height: 49 }
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'model', x: 261, y: 24, width: 80, height: 10 },
    { type: 'code-language', x: 357, y: 24, width: 47, height: 10 },
    { type: 'reset', x: 120, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 21, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 430, y: 23, width: 58, height: 11 }
  ]
}
工具条位置更新被节流
📍 工具条区域已更新: {
  regionsCount: 6,
  regions: [
    { type: 'model', x: 261, y: 24, width: 89, height: 10 },
    { type: 'code-language', x: 367, y: 24, width: 47, height: 10 },
    { type: 'reset', x: 120, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 21, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 439, y: 23, width: 58, height: 11 },
    { type: 'voice', x: 522, y: 21, width: 46, height: 16 }
  ]
}
工具条位置已更新: { x: 16, y: 12, width: 582, height: 49 }
📍 工具条区域已更新: {
  regionsCount: 6,
  regions: [
    { type: 'model', x: 261, y: 24, width: 89, height: 10 },
    { type: 'code-language', x: 367, y: 24, width: 51, height: 10 },
    { type: 'reset', x: 120, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 21, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 442, y: 23, width: 58, height: 11 },
    { type: 'voice', x: 526, y: 21, width: 46, height: 16 }
  ]
}
initializeApp: 开始检查并初始化配置...
checkAndInitializeConfig called
检测到已存储的 API Key，开始初始化配置...
开始初始化配置... sk-1192bc4bdb2e4fa5993198aeac83f010
configResult: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3',
      'deepseek-r1',
      'gemini-2.5',
      'claude',
      'grok-4'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
配置初始化成功
通知渲染进程更新麦克风按钮状态...
✅ 配置更新事件已发送到渲染进程
initializeApp: 开始请求系统权限...
initializeApp: 权限请求完成
initializeApp: 应用初始化完成
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3',
      'deepseek-r1',
      'gemini-2.5',
      'claude',
      'grok-4'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
🖱️ 检测到鼠标点击事件: { x: 569, y: 38, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 569, y: 38 },
  windowBounds: { x: 0, y: 0, width: 630, height: 73 },
  toolbarBounds: { x: 16, y: 12, width: 582, height: 49 },
  regionsCount: 6
}
使用渲染进程报告的工具条位置: { x: 16, y: 12, width: 582, height: 49 }
鼠标位置 (569, 38) 在工具条范围内
点击在 voice 区域内
Voice区域被点击，显示语音识别界面
工具条位置已更新: { x: 5, y: 1, width: 748, height: 749 }
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 688, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 102, y: 59, width: 28, height: 28 },
    { type: 'voice-system-audio', x: 66, y: 59, width: 28, height: 28 },
    {
      type: 'voice-one-click-start',
      x: 138,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 170, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 688, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 102, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 66, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 138,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 170, height: 36, type: 'voice-send-to-ai' }
]
📍 更新AI回复框位置信息: {
  fastResponse: { x: 192, y: 50, width: 283, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 475,
    y: 50,
    width: 279,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 192, y: 50, width: 283, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 475,
    y: 50,
    width: 279,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 200, y: 50, width: 295, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 495,
    y: 50,
    width: 291,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 216, y: 50, width: 319, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 535,
    y: 50,
    width: 315,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 224, y: 50, width: 331, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 555,
    y: 50,
    width: 327,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 232, y: 50, width: 343, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 575,
    y: 50,
    width: 339,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 232, y: 50, width: 343, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 575,
    y: 50,
    width: 339,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 240, y: 50, width: 355, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 595,
    y: 50,
    width: 351,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 256, y: 50, width: 379, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 635,
    y: 50,
    width: 375,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 272, y: 50, width: 403, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 675,
    y: 50,
    width: 399,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 280, y: 50, width: 415, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 695,
    y: 50,
    width: 411,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 288, y: 50, width: 427, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 715,
    y: 50,
    width: 423,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 296, y: 50, width: 439, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 735,
    y: 50,
    width: 435,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 296, y: 50, width: 439, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 735,
    y: 50,
    width: 435,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 312, y: 50, width: 463, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 775,
    y: 50,
    width: 459,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 312, y: 50, width: 463, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 775,
    y: 50,
    width: 459,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 320, y: 50, width: 475, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 795,
    y: 50,
    width: 471,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 328, y: 50, width: 487, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 815,
    y: 50,
    width: 483,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 344, y: 50, width: 511, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 855,
    y: 50,
    width: 507,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 352, y: 50, width: 523, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 875,
    y: 50,
    width: 519,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 360, y: 50, width: 535, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 895,
    y: 50,
    width: 531,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 368, y: 50, width: 547, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 915,
    y: 50,
    width: 543,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 376, y: 50, width: 559, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 935,
    y: 50,
    width: 555,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 384, y: 50, width: 571, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 955,
    y: 50,
    width: 567,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 384, y: 50, width: 571, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 955,
    y: 50,
    width: 567,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 400, y: 50, width: 595, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 995,
    y: 50,
    width: 591,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 414, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1012,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 419, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1017,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 446, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1044,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 435, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1033,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 462, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1060,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 451, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1049,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 478, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1076,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 467, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1065,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
工具条位置已更新: { x: 69, y: 1, width: 1590, height: 749 }
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🖱️ 检测到鼠标点击事件: { x: 29, y: 425, button: 1, clicks: 2 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 29, y: 425 },
  windowBounds: { x: 0, y: 0, width: 1760, height: 751 },
  toolbarBounds: { x: 69, y: 1, width: 1590, height: 749 },
  regionsCount: 5
}
使用渲染进程报告的工具条位置: { x: 69, y: 1, width: 1590, height: 749 }
鼠标位置 (29, 425) 不在工具条范围内
📍 更新AI回复框位置信息: {
  fastResponse: { x: 494, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1092,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 483, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1081,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 499, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1097,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 499, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1097,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 499, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1097,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 499, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1097,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 499, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1097,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 499, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1097,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 499, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1097,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
工具条位置已更新: { x: 101, y: 1, width: 1590, height: 749 }
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🖱️ 检测到鼠标点击事件: { x: 468, y: 77, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 468, y: 77 },
  windowBounds: { x: 0, y: 0, width: 1792, height: 751 },
  toolbarBounds: { x: 101, y: 1, width: 1590, height: 749 },
  regionsCount: 5
}
使用渲染进程报告的工具条位置: { x: 101, y: 1, width: 1590, height: 749 }
鼠标位置 (468, 77) 在工具条范围内
点击在 voice-one-click-start 区域内
语音面板一键启动按钮被点击
🚀 执行语音识别流程启动: 系统音频=true, 麦克风=true
📡 步骤1: 启动系统音频ASR连接...
Starting SystemASR session for system audio
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
SystemASRManager: 系统音频识别启用状态: true
SystemASR: Starting connection attempt (ID: 078ca0d2-314f-44bb-a13c-80d90875b3a3)
SystemASR: WebSocket connection established
SystemASR: 发送初始化消息
连接状态更新: systemASR = true {
  systemASR: true,
  microphoneASR: false,
  systemAudio: false,
  microphone: false
}
✅ 系统音频ASR连接成功
SystemASR: Received FULL_SERVER_RESPONSE
SystemASR: Processing transcript response: {"audio_info":{"duration":0},"result":{"additions":{"log_id":"20250724183450E5D0BC0591F9BC14661A"},"text":""}}
SystemASR: New session started with ID: 20250724183450E5D0BC0591F9BC14661A
SystemASR: 距离上次转写超过30秒，清空临时记录
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
📡 启动系统音频捕获...
SystemAudioManager: 开始启动系统音频捕获
SystemAudioManager: 第一步 - 启动SystemASR服务
Starting SystemASR session for system audio
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
SystemASRManager: 系统音频识别启用状态: true
SystemASR: Active WebSocket connection already exists
SystemAudioManager: SystemASR服务启动成功
SystemAudioManager: 第二步 - 启动系统音频捕获
SystemAudioManager: macOS音频捕获命令: /Users/<USER>/Desktop/coder-master/bin/macos/system-audio-capture --output /Users/<USER>/Library/Application Support/secure-kernel/temp/system-audio.pcm
SystemAudioManager: 启动音频捕获进程... (尝试 1/4)
SystemAudioManager: 音频捕获进程已启动
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
SystemAudioManager: stdout: {"code":"RECORDING_STARTED","channels":2,"path":"\/Users\/<USER>\/Library\/Application Support\/secure-kernel\/temp\/system-audio.pcm","format":"PCM 16-bit","sampleRate":48000,"timestamp":"2025-07-24T10:34:52Z"}

SystemAudioManager: 录音已开始，文件保存在: /Users/<USER>/Library/Application Support/secure-kernel/temp/system-audio.pcm
macOS音频参数: 采样率=48000, 通道数=2, 位数=16
SystemAudioManager: 输出文件大小: 4096字节
开始监控PCM文件 (安全版本): /Users/<USER>/Library/Application Support/secure-kernel/temp/system-audio.pcm
采用安全读取机制，避免读写竞争
位置指针管理器已重置
启动音频文件监控，首次读取将在200ms后开始
连接状态更新: systemAudio = true {
  systemASR: true,
  microphoneASR: false,
  systemAudio: true,
  microphone: false
}
✅ 系统音频捕获启动成功
🎤 步骤2: 启动麦克风ASR连接...
🎤 Starting MicrophoneASR session for microphone audio
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
MicrophoneASRManager: 麦克风音频识别启用状态: true
MicrophoneASR: Starting connection attempt (ID: 6848eaad-7aa1-46d3-9619-1433202c1453)
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
MicrophoneASR: WebSocket connection established
MicrophoneASR: 发送初始化消息
连接状态更新: microphoneASR = true {
  systemASR: true,
  microphoneASR: true,
  systemAudio: true,
  microphone: false
}
✅ 麦克风ASR连接成功
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":0},"result":{"additions":{"log_id":"20250724183453076E2BE8E2C33F157FDF"},"text":""}}
MicrophoneASR: New session started with ID: 20250724183453076E2BE8E2C33F157FDF
MicrophoneASR: 距离上次转写超过15秒，清空临时记录
安全读取: 位置=0, 文件大小=57856, 待读取=57856字节
帧处理: 总数据=57856字节, 完整帧=57856字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57856字节, 缓存完整帧=0字节, 有效帧=57856字节, 新不完整帧=0字节
当前文件位置: 57856字节, 总读取: 0字节
发送音频数据: 9642字节, 4821样本, 能量=509.23, 静音=false
SystemASR: 处理音频数据，长度=9642字节, 采样率=16000Hz
安全读取并处理 57856 字节音频数据，新位置: 57856
SystemASR: 发送音频数据，序列号=2, 总大小=113字节
SystemASR: Received FULL_SERVER_RESPONSE
SystemASR: Processing transcript response: {"audio_info":{"duration":301},"result":{"additions":{"log_id":"20250724183450E5D0BC0591F9BC14661A"},"text":""}}
安全读取: 位置=57856, 文件大小=119296, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 119296字节, 总读取: 57856字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true
SystemASR: 检测到几乎完全静音数据，跳过发送
安全读取并处理 61440 字节音频数据，新位置: 119296
安全读取: 位置=119296, 文件大小=180736, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 180736
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎤 启动麦克风捕获...
🎤 MicrophoneManager: 开始启动麦克风音频捕获
🎤 MicrophoneManager: 第一步 - 启动MicrophoneASRManager服务
🎤 Starting MicrophoneASR session for microphone audio
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
MicrophoneASRManager: 麦克风音频识别启用状态: true
✅ MicrophoneASR: Active WebSocket connection already exists
🎤 MicrophoneManager: MicrophoneASRManager启动结果: { success: true }
✅ MicrophoneManager: MicrophoneASRManager服务启动成功
🎤 MicrophoneManager: 第二步 - 启动麦克风音频捕获
📡 MicrophoneManager: 发送麦克风状态到渲染进程
✅ MicrophoneManager: 已通知渲染进程录制开始
✅ MicrophoneManager: 麦克风音频捕获启动成功
连接状态更新: microphone = true {
  systemASR: true,
  microphoneASR: true,
  systemAudio: true,
  microphone: true
}
✅ 麦克风捕获启动成功
📡 发送 start-microphone-recognition 事件到渲染进程
🎯 语音识别流程启动结果: 整体=true, 系统音频=true, 麦克风=true
📊 连接状态: {
  systemASR: true,
  microphoneASR: true,
  systemAudio: true,
  microphone: true
}
安全读取: 位置=180736, 文件大小=242176, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 242176
安全读取: 位置=242176, 文件大小=299776, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
检测到静音开始
当前文件位置: 299776字节, 总读取: 242176字节
发送音频数据: 9600字节, 4800样本, 能量=0.00, 静音=true
SystemASR: 检测到几乎完全静音数据，跳过发送
安全读取并处理 57600 字节音频数据，新位置: 299776
安全读取: 位置=299776, 文件大小=365056, 待读取=65280字节
帧处理: 总数据=65280字节, 完整帧=65280字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65280字节, 缓存完整帧=0字节, 有效帧=65280字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 65280 字节音频数据，新位置: 365056
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
IPC: 收到启动麦克风音频捕获请求
🎤 MicrophoneManager: 开始启动麦克风音频捕获
🎤 MicrophoneManager: 麦克风已在录制中
IPC: 收到启动麦克风音频捕获请求
🎤 MicrophoneManager: 开始启动麦克风音频捕获
🎤 MicrophoneManager: 麦克风已在录制中
安全读取: 位置=365056, 文件大小=422656, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 57600 字节音频数据，新位置: 422656
[33970:0724/183455.079532:ERROR:system_services.cc(34)] SetApplicationIsDaemon: Error Domain=NSOSStatusErrorDomain Code=-50 "paramErr: error in user parameter list" (-50)
安全读取: 位置=422656, 文件大小=484096, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 484096
安全读取: 位置=484096, 文件大小=526336, 待读取=42240字节
帧处理: 总数据=42240字节, 完整帧=42240字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=42240字节, 缓存完整帧=0字节, 有效帧=42240字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 42240 字节音频数据，新位置: 526336
安全读取: 位置=526336, 文件大小=587776, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 587776
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3584
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1753353295845 处理麦克风音频数据，长度=3584字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0403, 动态范围: 72.08dB, 信噪比估计: 72.08dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=2, 总大小=1594字节, 原始长度=3584字节, 压缩率=44.14%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":112},"result":{"additions":{"log_id":"20250724183453076E2BE8E2C33F157FDF"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1753353295945 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0400, 动态范围: 70.77dB, 信噪比估计: 70.77dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=3, 总大小=2953字节, 原始长度=3328字节, 压缩率=88.37%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":216},"result":{"additions":{"log_id":"20250724183453076E2BE8E2C33F157FDF"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1753353296056 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0339, 动态范围: 67.96dB, 信噪比估计: 67.96dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=4, 总大小=2927字节, 原始长度=3328字节, 压缩率=87.59%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":320},"result":{"additions":{"log_id":"20250724183453076E2BE8E2C33F157FDF"},"text":""}}
安全读取: 位置=587776, 文件大小=649216, 待读取=61440字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1753353296155 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=5, 总大小=2782字节, 原始长度=3328字节, 压缩率=83.23%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 649216
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1753353296255 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=6, 总大小=2350字节, 原始长度=3328字节, 压缩率=70.25%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":424},"result":{"additions":{"log_id":"20250724183453076E2BE8E2C33F157FDF"},"text":""}}
🖱️ 检测到鼠标点击事件: { x: 471, y: 73, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 471, y: 73 },
  windowBounds: { x: 0, y: 0, width: 1792, height: 751 },
  toolbarBounds: { x: 101, y: 1, width: 1590, height: 749 },
  regionsCount: 5
}
使用渲染进程报告的工具条位置: { x: 101, y: 1, width: 1590, height: 749 }
鼠标位置 (471, 73) 在工具条范围内
点击在 voice-one-click-start 区域内
语音面板一键启动按钮被点击
🛑 收到停止语音识别流程请求: 系统音频=true, 麦克风=true
🔇 停止系统音频服务...
停止系统音频捕获
SystemASR: Stopping ASR session
SystemASR: Stopping session
开始终止系统音频捕获进程...
正在终止音频捕获进程 PID: 33943
✅ [2025-07-24T10:34:56.263Z] 进程操作: terminate_start (PID: 33943) - Starting termination process
第一步：尝试优雅终止进程...
✅ [2025-07-24T10:34:56.265Z] 进程操作: graceful_terminate (PID: 33943) - Sending SIGTERM
IPC: 收到停止麦克风音频捕获请求
🛑 MicrophoneManager: 停止麦克风音频捕获
🔇 MicrophoneManager: 通知MicrophoneASRManager停止处理音频
MicrophoneASR: Stopping ASR session
MicrophoneASR: Stopping session
📡 MicrophoneManager: 发送停止录制状态到渲染进程
✅ MicrophoneManager: 已通知渲染进程录制停止
SystemAudioManager: 音频捕获进程退出，代码: null, 信号: SIGTERM
✅ 进程已优雅退出
✅ [2025-07-24T10:34:56.270Z] 进程操作: graceful_exit (PID: 33943) - Process exited gracefully
✅ [2025-07-24T10:34:56.270Z] 进程操作: cleanup_complete (PID: 33943) - Process reference cleared
已删除系统音频输出文件
连接状态更新: systemAudio = false {
  systemASR: true,
  microphoneASR: true,
  systemAudio: false,
  microphone: true
}
✅ 系统音频捕获已停止
SystemASR: Stopping ASR session
SystemASR: Stopping session
连接状态更新: systemASR = false {
  systemASR: false,
  microphoneASR: true,
  systemAudio: false,
  microphone: true
}
✅ 系统音频ASR连接已关闭
🎤 停止麦克风服务...
🛑 MicrophoneManager: 停止麦克风音频捕获
⚠️ MicrophoneManager: 麦克风未在录制中
连接状态更新: microphone = false {
  systemASR: false,
  microphoneASR: true,
  systemAudio: false,
  microphone: false
}
✅ 麦克风捕获已停止
MicrophoneASR: Stopping ASR session
MicrophoneASR: Stopping session
连接状态更新: microphoneASR = false {
  systemASR: false,
  microphoneASR: false,
  systemAudio: false,
  microphone: false
}
✅ 麦克风ASR连接已关闭
📊 停止后连接状态: {
  systemASR: false,
  microphoneASR: false,
  systemAudio: false,
  microphone: false
}
音频文件监控已停止，缓存已清理
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🖱️ 检测到鼠标点击事件: { x: 1286, y: 1117, button: 1, clicks: 2 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 1286, y: 1117 },
  windowBounds: { x: 0, y: 0, width: 1792, height: 751 },
  toolbarBounds: { x: 101, y: 1, width: 1590, height: 749 },
  regionsCount: 5
}
使用渲染进程报告的工具条位置: { x: 101, y: 1, width: 1590, height: 749 }
鼠标位置 (1286, 1117) 不在工具条范围内
App 'before-quit' event fired
正在停止鼠标监听程序...
开始停止ioHook...
ioHook事件监听器已移除
ioHook.stop()调用完成
ioHook停止完成
正在清理全局快捷键...
快捷键 Alt+C 已注销
快捷键 Alt+V 已注销
快捷键 Alt+A 已注销
快捷键 CommandOrControl+Left 已注销
快捷键 CommandOrControl+Right 已注销
快捷键 CommandOrControl+Down 已注销
快捷键 CommandOrControl+Up 已注销
快捷键 CommandOrControl+B 已注销
快捷键 Alt+Z 已注销
快捷键 Alt+2 已注销
快捷键 Alt+3 已注销
快捷键 Alt+1 已注销
快捷键 CommandOrControl+Q 已注销
所有快捷键已注销
键盘钩子已停用
正在取消处理请求...
App 'will-quit' event fired
App 'quit' event fired - 开始最终清理
清理 SystemAudioManager...
停止系统音频捕获
系统音频捕获未启动，无需停止
清理 MicrophoneManager...
🛑 MicrophoneManager: 停止麦克风音频捕获
⚠️ MicrophoneManager: 麦克风未在录制中
所有资源清理完成
