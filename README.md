# 实时语音识别系统

基于现有asar项目的实时语音识别功能，整合了麦克风和系统音频的实时识别能力。

## 功能特性

### 🎤 双音频源支持
- **麦克风音频识别**：实时识别用户语音输入
- **系统音频识别**：捕获并识别系统播放的音频内容
- **同步处理**：支持两个音频源同时工作

### 🔇 智能静音处理
- **自适应静音检测**：根据音频源类型调整静音阈值
- **噪音过滤**：过滤低质量和噪音音频
- **静音状态管理**：智能处理静音期间的状态变化

### 📝 文案收集与格式化
- **角色区分**：系统音频作为"提问方"，麦克风音频作为"回复方"
- **智能拼接**：按时间顺序整理并格式化转写内容
- **长度控制**：系统音频最近150字符，麦克风音频最近50字符
- **去重处理**：避免重复内容影响AI理解

### ⚡ 性能优化
- **缓冲区管理**：智能音频缓冲和批处理
- **内存控制**：限制历史记录和缓存大小
- **性能监控**：实时监控处理性能和资源使用

## 核心文件说明

### 1. RealtimeVoiceRecognition.ts
主要的语音识别管理器，负责：
- 音频数据处理和质量分析
- 静音检测和状态管理
- 转写结果收集和格式化
- 性能监控和资源管理

### 2. RealtimeVoiceRecognitionExample.ts
使用示例和服务封装，包含：
- IPC处理程序注册
- 事件监听和处理
- 前端集成示例

### 3. VoiceRecognitionConfig.ts
配置管理系统，提供：
- 多种预设配置模式
- 音频处理参数配置
- ASR服务配置管理
- 配置验证和导入导出

## 使用方法

### 1. 基本集成

```typescript
import { VoiceRecognitionService } from './RealtimeVoiceRecognitionExample';
import { VoiceRecognitionConfig } from './VoiceRecognitionConfig';

// 创建服务实例
const voiceService = new VoiceRecognitionService();

// 配置参数
const config: VoiceRecognitionConfig = {
  voiceAppId: 'your-app-id',
  voiceAccessKeyId: 'your-access-key',
  enableMicrophone: true,
  enableSystemAudio: true,
  silenceThreshold: 0.005,
  noiseGateThreshold: 0.003,
  maxBufferSize: 32000,
  minBufferSize: 6400
};

// 初始化服务
await voiceService.initialize(mainWindow, config);

// 启动语音识别
const result = await voiceService.startRecognition();
if (result.success) {
  console.log('语音识别已启动');
} else {
  console.error('启动失败:', result.error);
}
```

### 2. 处理音频数据

```typescript
// 处理麦克风音频
await voiceService.processMicrophoneAudio(audioBuffer, 16000);

// 处理系统音频
await voiceService.processSystemAudio(audioBuffer, 16000);
```

### 3. 获取格式化文案

```typescript
// 获取用于发送给AI的格式化文案
const formattedText = voiceService.getFormattedTranscriptions();
console.log('发送给AI的文案:', formattedText);

// 格式示例：
// "提问方：请帮我写一个计算两个数之和的函数；回复方：好的，我来帮你写；"
```

### 4. 前端集成

```javascript
// 启动语音识别
const startRecognition = async () => {
  const result = await window.electronAPI.invoke('voice-recognition:start');
  if (result.success) {
    console.log('语音识别已启动');
  }
};

// 监听转写结果
window.electronAPI.on('asr:transcription', (data) => {
  if (data.source === 'microphone') {
    updateMicrophoneTranscript(data.text, data.isFinal);
  } else if (data.source === 'system') {
    updateSystemTranscript(data.text, data.isFinal);
  }
});

// 点击"请求AI"按钮
const handleSendToAI = async () => {
  const result = await window.electronAPI.invoke('voice-recognition:get-formatted-text');
  if (result.success && result.text.trim()) {
    sendToAI(result.text);
  }
};
```

## 配置说明

### 预设配置模式

1. **高性能模式** (`highPerformance`)
   - 优先实时性，降低延迟
   - 适用于实时对话场景

2. **高精度模式** (`highAccuracy`)
   - 优先准确性，提高识别质量
   - 适用于重要内容转写

3. **平衡模式** (`balanced`)
   - 平衡实时性和准确性
   - 适用于日常使用

4. **低功耗模式** (`lowPower`)
   - 减少资源消耗
   - 适用于长时间运行

### 关键参数说明

```typescript
{
  // 静音检测阈值（越小越敏感）
  silenceThreshold: 0.005,
  
  // 噪音门限阈值
  noiseGateThreshold: 0.003,
  
  // 最大缓冲区大小（字节）
  maxBufferSize: 32000,
  
  // 最小缓冲区大小（字节）
  minBufferSize: 6400,
  
  // 静音超时时间（毫秒）
  silenceTimeoutMs: 5000
}
```

## 核心逻辑解析

### 1. 语音识别后点击请求AI时的文案确定逻辑

当用户点击"请求AI"按钮时，系统会执行以下步骤：

1. **收集所有转写内容**
   ```typescript
   // 收集历史记录和实时转写
   const allTranscriptions = [
     ...this.transcriptionHistory,  // 历史最终结果
     ...this.realtimeTranscripts    // 当前实时转写
   ];
   ```

2. **按时间排序**
   ```typescript
   allTranscriptions.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
   ```

3. **按音频源分组**
   ```typescript
   const systemTexts = allTranscriptions.filter(item => item.source === 'system');
   const microphoneTexts = allTranscriptions.filter(item => item.source === 'microphone');
   ```

4. **合并并限制长度**
   ```typescript
   const systemContent = systemTexts.join('').slice(-150);      // 系统音频最近150字符
   const microphoneContent = microphoneTexts.join('').slice(-50); // 麦克风最近50字符
   ```

5. **格式化最终文案**
   ```typescript
   let formattedText = '';
   if (systemContent) {
     formattedText += `提问方：${systemContent}；`;
   }
   if (microphoneContent) {
     formattedText += `回复方：${microphoneContent}；`;
   }
   ```

### 2. 静音状态处理逻辑

系统通过多层静音检测机制确保音频质量：

1. **音频质量分析**
   ```typescript
   const quality = this.analyzeAudioQuality(audioBuffer, source);
   // 检查RMS、动态范围、信噪比等指标
   ```

2. **静音状态判断**
   ```typescript
   const isSilent = (
     rms < silenceThreshold ||           // RMS低于阈值
     nonZeroRatio < 0.01 ||             // 非零样本比例过低
     dynamicRange < 6 ||                // 动态范围不足
     snrEstimate < 3                    // 信噪比过低
   );
   ```

3. **静音状态处理**
   ```typescript
   if (quality.isSilent) {
     this.handleSilentAudio(source, timestamp);
     // 更新静音计数器，长时间静音时清空实时转写
   } else {
     this.handleValidAudio(audioBuffer, source, sampleRate, timestamp, quality);
     // 处理有效音频，更新缓冲区
   }
   ```

4. **静音恢复处理**
   ```typescript
   if (silenceState?.isSilent && !quality.isSilent) {
     // 从静音状态恢复，重置计数器
     silenceState.isSilent = false;
     silenceState.consecutiveSilentChunks = 0;
   }
   ```

## 性能监控

系统提供详细的性能监控功能：

- **音频处理统计**：总处理量、转写数量、静音过滤数量
- **内存使用监控**：堆内存、外部内存、RSS使用情况
- **连接状态监控**：ASR服务连接状态和重连统计
- **实时性能报告**：定期输出性能指标

## 注意事项

1. **配置ASR服务**：需要正确配置`voiceAppId`和`voiceAccessKeyId`
2. **音频格式**：支持16kHz、16位、单声道PCM格式
3. **内存管理**：系统会自动限制历史记录和缓存大小
4. **错误处理**：包含完整的错误处理和恢复机制
5. **资源清理**：使用完毕后调用`cleanup()`方法清理资源

## 扩展功能

- 支持多种ASR服务提供商
- 可配置的音频预处理算法
- 自定义转写结果后处理
- 支持语音命令检测
- 可扩展的性能监控指标
