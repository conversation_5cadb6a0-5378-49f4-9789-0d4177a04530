/**
 * VoiceRecognitionConfig.ts
 * 语音识别配置管理
 * 
 * 提供统一的配置管理，包括：
 * 1. 音频处理参数
 * 2. 静音检测配置
 * 3. 缓冲区管理设置
 * 4. ASR服务配置
 */

export interface AudioProcessingConfig {
  // 采样率配置
  sampleRate: number;
  channels: number;
  bitsPerSample: number;
  
  // 缓冲区配置
  maxBufferSize: number;
  minBufferSize: number;
  optimalChunkSize: number;
  
  // 静音检测配置
  silenceThreshold: number;
  noiseGateThreshold: number;
  silenceTimeoutMs: number;
  
  // 音频质量配置
  minRMS: number;
  minDynamicRange: number;
  minSNR: number;
  minNonZeroRatio: number;
}

export interface ASRServiceConfig {
  // 服务连接配置
  appId: string;
  accessKey: string;
  endpoint: string;
  
  // ASR模型配置
  modelName: string;
  mode: string;
  language: string;
  domain: string;
  
  // 实时性配置
  latency: number;
  refreshMs: number;
  enablePartialResult: boolean;
  
  // 功能开关
  enablePunctuation: boolean;
  enableITN: boolean;
  enableVAD: boolean;
  enableWords: boolean;
  enableConfidence: boolean;
  
  // 过滤配置
  enableDisfluencyFilter: boolean;
  enableFillerWordsFilter: boolean;
}

export interface VoiceRecognitionConfig {
  // 音频源配置
  enableMicrophone: boolean;
  enableSystemAudio: boolean;
  
  // 音频处理配置
  microphone: AudioProcessingConfig;
  systemAudio: AudioProcessingConfig;
  
  // ASR服务配置
  asrService: ASRServiceConfig;
  
  // 转写文案配置
  transcription: {
    maxHistoryItems: number;
    systemAudioMaxChars: number;
    microphoneMaxChars: number;
    enableDeduplication: boolean;
    deduplicationThreshold: number;
  };
  
  // 性能配置
  performance: {
    enablePerformanceMonitoring: boolean;
    reportIntervalMs: number;
    maxMemoryUsageMB: number;
    enableDebugLogging: boolean;
  };
  
  // UI配置
  ui: {
    showRealTimeTranscription: boolean;
    showAudioLevels: boolean;
    showPerformanceMetrics: boolean;
    autoScrollTranscription: boolean;
  };
}

/**
 * 默认配置
 */
export const DEFAULT_CONFIG: VoiceRecognitionConfig = {
  enableMicrophone: true,
  enableSystemAudio: true,
  
  microphone: {
    sampleRate: 16000,
    channels: 1,
    bitsPerSample: 16,
    maxBufferSize: 32000,
    minBufferSize: 6400,
    optimalChunkSize: 12800,
    silenceThreshold: 0.005,
    noiseGateThreshold: 0.003,
    silenceTimeoutMs: 5000,
    minRMS: 0.001,
    minDynamicRange: 6,
    minSNR: 3,
    minNonZeroRatio: 0.01
  },
  
  systemAudio: {
    sampleRate: 16000,
    channels: 1,
    bitsPerSample: 16,
    maxBufferSize: 32000,
    minBufferSize: 6400,
    optimalChunkSize: 12800,
    silenceThreshold: 0.003,
    noiseGateThreshold: 0.002,
    silenceTimeoutMs: 5000,
    minRMS: 0.0005,
    minDynamicRange: 6,
    minSNR: 3,
    minNonZeroRatio: 0.01
  },
  
  asrService: {
    appId: '',
    accessKey: '',
    endpoint: 'wss://openspeech.bytedance.com/api/v3/sauc/bigmodel',
    modelName: 'bigmodel',
    mode: '2pass-fast',
    language: 'zh-CN',
    domain: 'general',
    latency: 1,
    refreshMs: 300,
    enablePartialResult: true,
    enablePunctuation: true,
    enableITN: true,
    enableVAD: true,
    enableWords: true,
    enableConfidence: true,
    enableDisfluencyFilter: true,
    enableFillerWordsFilter: true
  },
  
  transcription: {
    maxHistoryItems: 100,
    systemAudioMaxChars: 150,
    microphoneMaxChars: 50,
    enableDeduplication: true,
    deduplicationThreshold: 0.5
  },
  
  performance: {
    enablePerformanceMonitoring: true,
    reportIntervalMs: 30000,
    maxMemoryUsageMB: 100,
    enableDebugLogging: false
  },
  
  ui: {
    showRealTimeTranscription: true,
    showAudioLevels: true,
    showPerformanceMetrics: false,
    autoScrollTranscription: true
  }
};

/**
 * 预设配置模式
 */
export const PRESET_CONFIGS = {
  // 高性能模式：优先实时性
  highPerformance: {
    ...DEFAULT_CONFIG,
    microphone: {
      ...DEFAULT_CONFIG.microphone,
      maxBufferSize: 16000,
      minBufferSize: 3200,
      silenceThreshold: 0.01,
      silenceTimeoutMs: 3000
    },
    systemAudio: {
      ...DEFAULT_CONFIG.systemAudio,
      maxBufferSize: 16000,
      minBufferSize: 3200,
      silenceThreshold: 0.005,
      silenceTimeoutMs: 3000
    },
    asrService: {
      ...DEFAULT_CONFIG.asrService,
      mode: '2pass-fast',
      latency: 1,
      refreshMs: 200
    }
  },
  
  // 高精度模式：优先准确性
  highAccuracy: {
    ...DEFAULT_CONFIG,
    microphone: {
      ...DEFAULT_CONFIG.microphone,
      maxBufferSize: 64000,
      minBufferSize: 12800,
      silenceThreshold: 0.002,
      silenceTimeoutMs: 8000
    },
    systemAudio: {
      ...DEFAULT_CONFIG.systemAudio,
      maxBufferSize: 64000,
      minBufferSize: 12800,
      silenceThreshold: 0.001,
      silenceTimeoutMs: 8000
    },
    asrService: {
      ...DEFAULT_CONFIG.asrService,
      mode: '2pass',
      latency: 3,
      refreshMs: 500
    }
  },
  
  // 平衡模式：平衡实时性和准确性
  balanced: {
    ...DEFAULT_CONFIG
  },
  
  // 低功耗模式：减少资源消耗
  lowPower: {
    ...DEFAULT_CONFIG,
    microphone: {
      ...DEFAULT_CONFIG.microphone,
      maxBufferSize: 16000,
      minBufferSize: 6400,
      silenceThreshold: 0.02,
      silenceTimeoutMs: 10000
    },
    systemAudio: {
      ...DEFAULT_CONFIG.systemAudio,
      maxBufferSize: 16000,
      minBufferSize: 6400,
      silenceThreshold: 0.01,
      silenceTimeoutMs: 10000
    },
    performance: {
      ...DEFAULT_CONFIG.performance,
      enablePerformanceMonitoring: false,
      reportIntervalMs: 60000
    }
  }
};

/**
 * 配置管理器
 */
export class VoiceRecognitionConfigManager {
  private config: VoiceRecognitionConfig;
  private configPath: string;
  
  constructor(configPath?: string) {
    this.config = { ...DEFAULT_CONFIG };
    this.configPath = configPath || 'voice-recognition-config.json';
  }
  
  /**
   * 获取当前配置
   */
  public getConfig(): VoiceRecognitionConfig {
    return { ...this.config };
  }
  
  /**
   * 更新配置
   */
  public updateConfig(updates: Partial<VoiceRecognitionConfig>): void {
    this.config = { ...this.config, ...updates };
    console.log('VoiceRecognitionConfig: 配置已更新', updates);
  }
  
  /**
   * 应用预设配置
   */
  public applyPreset(presetName: keyof typeof PRESET_CONFIGS): void {
    const preset = PRESET_CONFIGS[presetName];
    if (preset) {
      this.config = { ...preset };
      console.log(`VoiceRecognitionConfig: 已应用预设配置 - ${presetName}`);
    } else {
      console.error(`VoiceRecognitionConfig: 未找到预设配置 - ${presetName}`);
    }
  }
  
  /**
   * 获取音频处理配置
   */
  public getAudioConfig(source: 'microphone' | 'systemAudio'): AudioProcessingConfig {
    return { ...this.config[source] };
  }
  
  /**
   * 获取ASR服务配置
   */
  public getASRConfig(): ASRServiceConfig {
    return { ...this.config.asrService };
  }
  
  /**
   * 验证配置有效性
   */
  public validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // 验证ASR服务配置
    if (!this.config.asrService.appId) {
      errors.push('ASR服务AppId未配置');
    }
    
    if (!this.config.asrService.accessKey) {
      errors.push('ASR服务AccessKey未配置');
    }
    
    // 验证音频配置
    if (this.config.microphone.maxBufferSize <= this.config.microphone.minBufferSize) {
      errors.push('麦克风最大缓冲区大小必须大于最小缓冲区大小');
    }
    
    if (this.config.systemAudio.maxBufferSize <= this.config.systemAudio.minBufferSize) {
      errors.push('系统音频最大缓冲区大小必须大于最小缓冲区大小');
    }
    
    // 验证阈值配置
    if (this.config.microphone.silenceThreshold <= 0) {
      errors.push('麦克风静音阈值必须大于0');
    }
    
    if (this.config.systemAudio.silenceThreshold <= 0) {
      errors.push('系统音频静音阈值必须大于0');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  /**
   * 重置为默认配置
   */
  public resetToDefault(): void {
    this.config = { ...DEFAULT_CONFIG };
    console.log('VoiceRecognitionConfig: 已重置为默认配置');
  }
  
  /**
   * 导出配置为JSON
   */
  public exportConfig(): string {
    return JSON.stringify(this.config, null, 2);
  }
  
  /**
   * 从JSON导入配置
   */
  public importConfig(configJson: string): { success: boolean; error?: string } {
    try {
      const importedConfig = JSON.parse(configJson);
      
      // 验证导入的配置结构
      if (this.isValidConfigStructure(importedConfig)) {
        this.config = { ...DEFAULT_CONFIG, ...importedConfig };
        console.log('VoiceRecognitionConfig: 配置导入成功');
        return { success: true };
      } else {
        return { success: false, error: '配置结构无效' };
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '配置解析失败' 
      };
    }
  }
  
  /**
   * 验证配置结构
   */
  private isValidConfigStructure(config: any): boolean {
    // 简单的结构验证
    return (
      typeof config === 'object' &&
      config !== null &&
      typeof config.enableMicrophone === 'boolean' &&
      typeof config.enableSystemAudio === 'boolean' &&
      typeof config.microphone === 'object' &&
      typeof config.systemAudio === 'object' &&
      typeof config.asrService === 'object'
    );
  }
}

/**
 * 创建配置管理器实例
 */
export function createConfigManager(configPath?: string): VoiceRecognitionConfigManager {
  return new VoiceRecognitionConfigManager(configPath);
}
