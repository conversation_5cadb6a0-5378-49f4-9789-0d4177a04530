import React, { useEffect, useRef, useState } from 'react';

// Define the types here since the global declaration may not be getting applied
interface ToolbarBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface ToolbarRegion {
  x: number;
  y: number;
  width: number;
  height: number;
  type: 'model' | 'code-language' | 'reset' | 'capture' | 'solution' | 'other' | 'voice' | 'voice-back' | 'voice-microphone' | 'voice-system-audio' | 'voice-one-click-start' | 'voice-send-to-ai';
}

interface BoundsResponse {
  success: boolean;
  error?: string;
}

/**
 * Component that measures the position of its child component and reports it to the main process
 */
interface ToolbarPositionReporterProps {
  children: React.ReactNode;
  modelSelector?: string; // CSS selector for the model region
  codeLanguageSelector?: string; // CSS selector for the code language region
  resetSelector?: string; // CSS selector for the reset button region
  captureSelector?: string; // CSS selector for the capture region
  solutionSelector?: string; // CSS selector for the solution button region
  voiceSelector?: string; // CSS selector for the voice assistant button region
  // Voice panel specific selectors
  backSelector?: string; // CSS selector for the back button in voice panel
  microphoneSelector?: string; // CSS selector for the microphone button in voice panel
  systemAudioSelector?: string; // CSS selector for the system audio button in voice panel
  oneClickStartSelector?: string; // CSS selector for the one-click start button in voice panel
  sendToAISelector?: string; // CSS selector for the send to AI button in voice panel
  errorState?: boolean; // Whether the app is in an error state (default: false)
  currentView?: "queue" | "solutions" | "debug" | "voice"; // 当前视图
}

const ToolbarPositionReporter: React.FC<ToolbarPositionReporterProps> = ({
  children,
  modelSelector = '.model-info', // Default selector for model region
  codeLanguageSelector = '.code-language-info', // Default selector for code language region
  resetSelector = '.reset-button', // Default selector for reset button
  captureSelector = '.capture-area', // Default selector for capture area
  solutionSelector = '.solution-button', // Default selector for solution button
  voiceSelector = '.voice-assistant', // Default selector for voice assistant button
  // Voice panel specific selectors
  backSelector = '.voice-back-button',
  microphoneSelector = '.voice-microphone-button',
  systemAudioSelector = '.voice-system-audio-button',
  oneClickStartSelector = '.voice-one-click-start-button',
  sendToAISelector = '.voice-send-to-ai-button',
  errorState = false, // Default to not being in error state
  currentView = 'queue', // Default to queue view
}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const [lastReportedBounds, setLastReportedBounds] = useState<ToolbarBounds | null>(null);
  const [reportCount, setReportCount] = useState<number>(0);
  const lastReportTimeRef = useRef<number>(0);
  const MIN_REPORT_INTERVAL = 250; // Minimum time between reports in milliseconds
  const ERROR_STATE_REPORT_LIMIT = 3; // Maximum number of reports to send when in error state
  const isWindows = navigator.platform.toLowerCase().includes('win');
  
  // Function to check if the bounds have changed significantly
  const hasBoundsChanged = (oldBounds: ToolbarBounds | null, newBounds: ToolbarBounds): boolean => {
    if (!oldBounds) return true;
    
    // Only consider changes greater than 1px to be significant
    return (
      Math.abs(oldBounds.x - newBounds.x) > 1 ||
      Math.abs(oldBounds.y - newBounds.y) > 1 ||
      Math.abs(oldBounds.width - newBounds.width) > 1 ||
      Math.abs(oldBounds.height - newBounds.height) > 1
    );
  };
  
  // Function to measure and report the toolbar position and its regions
  const reportPosition = () => {
    if (!elementRef.current) return;

    const now = Date.now();
    // Throttle updates to prevent rapid-fire reporting
    const timeSinceLastReport = now - lastReportTimeRef.current;
    if (timeSinceLastReport < MIN_REPORT_INTERVAL) {
      return;
    }
    
    // If in error state and we've already reported position a few times, don't report again
    if (errorState && reportCount >= ERROR_STATE_REPORT_LIMIT) {
      console.debug('In error state and already reported position limit reached, skipping update');
      return;
    }
    
    const toolbarRect = elementRef.current.getBoundingClientRect();
    const devicePixelRatio = window.devicePixelRatio || 1;
    
    // 为Windows高DPI屏幕添加额外的宽度调整
    let widthAdjustment = 0;
    if (isWindows && devicePixelRatio > 1) {
      // 根据DPI缩放比例增加额外的宽度
      widthAdjustment = Math.ceil(20 * (devicePixelRatio - 1));
    }
    
    const toolbarBounds: ToolbarBounds = {
      x: Math.round(toolbarRect.left),
      y: Math.round(toolbarRect.top),
      width: Math.round(toolbarRect.width + widthAdjustment),
      height: Math.round(toolbarRect.height)
    };
    
    // Skip if bounds haven't changed significantly
    if (!hasBoundsChanged(lastReportedBounds, toolbarBounds)) {
      console.debug('Toolbar bounds have not changed significantly, skipping update');
      return;
    }
    
    // First report the overall toolbar bounds
    if (typeof window.electronAPI?.updateToolbarBounds === 'function') {
      lastReportTimeRef.current = now;
      window.electronAPI.updateToolbarBounds(toolbarBounds)
        .then((result: BoundsResponse) => {
          if (result.success) {
            setLastReportedBounds(toolbarBounds);
            setReportCount(prevCount => prevCount + 1);
          } else {
            console.error('Failed to update toolbar bounds', result.error);
          }
        })
        .catch((err: Error) => {
          console.error('Error updating toolbar bounds:', err);
        });
    }
    
    // Then identify and report specific regions
    if (errorState) {
      // Don't report regions in error state to reduce processing
      return;
    }
    
    const regions: ToolbarRegion[] = [];
    
    // Try to find the model region
    const modelElement = elementRef.current.querySelector(modelSelector);
    if (modelElement) {
      const modelRect = modelElement.getBoundingClientRect();
      // Calculate position relative to the toolbar
      regions.push({
        x: Math.round(modelRect.left - toolbarRect.left),
        y: Math.round(modelRect.top - toolbarRect.top),
        width: Math.round(modelRect.width),
        height: Math.round(modelRect.height),
        type: 'model'
      });
    }

    // Try to find the code language region
    const codeLanguageElement = elementRef.current.querySelector(codeLanguageSelector);
    if (codeLanguageElement) {
      const codeLanguageRect = codeLanguageElement.getBoundingClientRect();
      // Calculate position relative to the toolbar
      regions.push({
        x: Math.round(codeLanguageRect.left - toolbarRect.left),
        y: Math.round(codeLanguageRect.top - toolbarRect.top),
        width: Math.round(codeLanguageRect.width),
        height: Math.round(codeLanguageRect.height),
        type: 'code-language'
      });
    }
    
    // Try to find the reset button region
    const resetElement = elementRef.current.querySelector(resetSelector);
    if (resetElement) {
      const resetRect = resetElement.getBoundingClientRect();
      // Calculate position relative to the toolbar
      regions.push({
        x: Math.round(resetRect.left - toolbarRect.left),
        y: Math.round(resetRect.top - toolbarRect.top),
        width: Math.round(resetRect.width),
        height: Math.round(resetRect.height),
        type: 'reset'
      });
    }
    
    // Try to find the capture area region
    const captureElement = elementRef.current.querySelector(captureSelector);
    if (captureElement) {
      const captureRect = captureElement.getBoundingClientRect();
      // Calculate position relative to the toolbar
      regions.push({
        x: Math.round(captureRect.left - toolbarRect.left),
        y: Math.round(captureRect.top - toolbarRect.top),
        width: Math.round(captureRect.width),
        height: Math.round(captureRect.height),
        type: 'capture'
      });
    }
    
    // Try to find the solution button region
    const solutionElement = elementRef.current.querySelector(solutionSelector);
    if (solutionElement) {
      const solutionRect = solutionElement.getBoundingClientRect();
      // Calculate position relative to the toolbar
      regions.push({
        x: Math.round(solutionRect.left - toolbarRect.left),
        y: Math.round(solutionRect.top - toolbarRect.top),
        width: Math.round(solutionRect.width),
        height: Math.round(solutionRect.height),
        type: 'solution'
      });
    }
    
    // Try to find the voice assistant button region (Queue页面的voice图标)
    const voiceElement = elementRef.current.querySelector(voiceSelector);
    console.log('🔍 查找语音助手按钮:', {
      selector: voiceSelector,
      found: !!voiceElement,
      currentView,
      elementExists: !!elementRef.current
    });

    if (voiceElement) {
      const voiceRect = voiceElement.getBoundingClientRect();
      // Calculate position relative to the toolbar
      const voiceRegion = {
        x: Math.round(voiceRect.left - toolbarRect.left),
        y: Math.round(voiceRect.top - toolbarRect.top),
        width: Math.round(voiceRect.width),
        height: Math.round(voiceRect.height),
        type: 'voice' as const
      };
      regions.push(voiceRegion);
      console.log('✅ 语音助手按钮区域已添加:', voiceRegion);
    } else if (currentView === 'queue') {
      // 在Queue页面中，如果没有找到voice按钮，输出更多调试信息
      console.warn('⚠️ Queue页面中未找到语音助手按钮，检查DOM结构...');
      const allVoiceElements = elementRef.current.querySelectorAll('[class*="voice"]');
      console.log('🔍 所有包含voice的元素:', Array.from(allVoiceElements).map(el => ({
        className: el.className,
        textContent: el.textContent?.trim()
      })));
    }

    // Voice panel specific buttons (only when in voice view)
    if (currentView === 'voice') {
      console.log('🔍 语音视图 - 开始查找语音面板按钮...');
      console.log('🔍 DOM容器:', elementRef.current);
      console.log('🔍 DOM容器内容:', elementRef.current?.innerHTML?.substring(0, 200) + '...');

      // 检查所有可能的按钮选择器
      const allButtons = elementRef.current.querySelectorAll('button');
      console.log('🔍 找到的所有按钮数量:', allButtons.length);
      allButtons.forEach((btn, index) => {
        console.log(`🔍 按钮 ${index}:`, {
          className: btn.className,
          textContent: btn.textContent?.trim(),
          hasVoiceClass: btn.className.includes('voice-')
        });
      });

      // Try to find the back button region
      const backElement = elementRef.current.querySelector(backSelector);
      console.log('🔍 查找返回按钮:', { selector: backSelector, found: !!backElement });
      if (backElement) {
        const backRect = backElement.getBoundingClientRect();
        regions.push({
          x: Math.round(backRect.left - toolbarRect.left),
          y: Math.round(backRect.top - toolbarRect.top),
          width: Math.round(backRect.width),
          height: Math.round(backRect.height),
          type: 'voice-back'
        });
        console.log('✅ 返回按钮区域已添加:', backRect);
      }

      // Try to find the microphone button region
      const microphoneElement = elementRef.current.querySelector(microphoneSelector);
      console.log('🔍 查找麦克风按钮:', { selector: microphoneSelector, found: !!microphoneElement });
      if (microphoneElement) {
        const microphoneRect = microphoneElement.getBoundingClientRect();
        regions.push({
          x: Math.round(microphoneRect.left - toolbarRect.left),
          y: Math.round(microphoneRect.top - toolbarRect.top),
          width: Math.round(microphoneRect.width),
          height: Math.round(microphoneRect.height),
          type: 'voice-microphone'
        });
        console.log('✅ 麦克风按钮区域已添加:', microphoneRect);
      }

      // Try to find the system audio button region
      const systemAudioElement = elementRef.current.querySelector(systemAudioSelector);
      console.log('🔍 查找系统音频按钮:', { selector: systemAudioSelector, found: !!systemAudioElement });
      if (systemAudioElement) {
        const systemAudioRect = systemAudioElement.getBoundingClientRect();
        regions.push({
          x: Math.round(systemAudioRect.left - toolbarRect.left),
          y: Math.round(systemAudioRect.top - toolbarRect.top),
          width: Math.round(systemAudioRect.width),
          height: Math.round(systemAudioRect.height),
          type: 'voice-system-audio'
        });
        console.log('✅ 系统音频按钮区域已添加:', systemAudioRect);
      }

      // Try to find the one-click start button region
      const oneClickStartElement = elementRef.current.querySelector(oneClickStartSelector);
      console.log('🔍 查找一键启动按钮:', { selector: oneClickStartSelector, found: !!oneClickStartElement });
      if (oneClickStartElement) {
        const oneClickStartRect = oneClickStartElement.getBoundingClientRect();
        regions.push({
          x: Math.round(oneClickStartRect.left - toolbarRect.left),
          y: Math.round(oneClickStartRect.top - toolbarRect.top),
          width: Math.round(oneClickStartRect.width),
          height: Math.round(oneClickStartRect.height),
          type: 'voice-one-click-start'
        });
        console.log('✅ 一键启动按钮区域已添加:', oneClickStartRect);
      }

      // Try to find the send to AI button region
      const sendToAIElement = elementRef.current.querySelector(sendToAISelector);
      console.log('🔍 查找发送到AI按钮:', { selector: sendToAISelector, found: !!sendToAIElement });
      if (sendToAIElement) {
        const sendToAIRect = sendToAIElement.getBoundingClientRect();
        regions.push({
          x: Math.round(sendToAIRect.left - toolbarRect.left),
          y: Math.round(sendToAIRect.top - toolbarRect.top),
          width: Math.round(sendToAIRect.width),
          height: Math.round(sendToAIRect.height),
          type: 'voice-send-to-ai'
        });
        console.log('✅ 发送到AI按钮区域已添加:', sendToAIRect);
      }
    }
    
    // Report the regions if we found any
    console.log('📊 准备上报区域信息:', { regionsCount: regions.length, regions });
    if (regions.length > 0 && typeof window.electronAPI?.updateToolbarRegions === 'function') {
      window.electronAPI.updateToolbarRegions(regions)
        .then((result: BoundsResponse) => {
          if (result.success) {
            console.log('✅ 区域信息上报成功');
          } else {
            console.error('❌ 区域信息上报失败:', result.error);
          }
        })
        .catch((err: Error) => {
          console.error('❌ 区域信息上报出错:', err);
        });
    } else {
      console.log('⚠️ 没有区域需要上报或API不可用');
    }
  };
  
  // Reset report count when error state changes
  useEffect(() => {
    if (!errorState) {
      setReportCount(0);
    }
  }, [errorState]);
  
  useEffect(() => {
    if (!elementRef.current) return;

    // Report position immediately on mount, with a small delay to ensure DOM is ready
    const timer = setTimeout(() => {
      reportPosition();
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // 当currentView变化时，重新上报位置（解决从Voice页面返回Queue页面时voice图标失效的问题）
  useEffect(() => {
    if (!elementRef.current) return;

    console.log(`🔄 视图切换到: ${currentView}，重新上报工具条位置`);

    // 重置上次上报的边界，强制重新上报
    setLastReportedBounds(null);
    setReportCount(0);

    // 延迟上报，确保DOM已经更新
    const timer = setTimeout(() => {
      reportPosition();
    }, 200);

    return () => clearTimeout(timer);
  }, [currentView]);

  useEffect(() => {
    if (!elementRef.current) return;

    // 在语音视图中，设置定期检查机制，因为按钮可能是动态渲染的
    let intervalId: NodeJS.Timeout | null = null;
    if (currentView === 'voice') {
      console.log('🎤 语音视图 - 启动定期位置检查');
      intervalId = setInterval(() => {
        reportPosition();
      }, 1000); // 每秒检查一次
    }

    // Add window resize event listener with debounce
    let resizeTimeout: NodeJS.Timeout | null = null;
    const handleResize = () => {
      if (resizeTimeout) {
        clearTimeout(resizeTimeout);
      }
      resizeTimeout = setTimeout(() => {
        reportPosition();
      }, 200); // Debounce resize events
    };
    window.addEventListener('resize', handleResize);
    
    // Set up event listeners for specific events
    const cleanupFunctions: Array<() => void> = [];
    
    // Listen for window movement
    if (typeof window.electronAPI?.onWindowMoved === 'function') {
      const cleanup = window.electronAPI.onWindowMoved(() => {
        reportPosition();
      });
      cleanupFunctions.push(cleanup);
    }
    
    // Listen for view reset
    if (typeof window.electronAPI?.onResetView === 'function') {
      const cleanup = window.electronAPI.onResetView(() => {
        setTimeout(() => {
          reportPosition();
        }, 300);
      });
      cleanupFunctions.push(cleanup);
    }
    
    // Listen for screenshot taken
    if (typeof window.electronAPI?.onScreenshotTaken === 'function') {
      const cleanup = window.electronAPI.onScreenshotTaken(() => {
        setTimeout(() => {
          reportPosition();
        }, 300);
      });
      cleanupFunctions.push(cleanup);
    }
    
    // Listen for model change
    if (typeof window.electronAPI?.onModelChanged === 'function') {
      const cleanup = window.electronAPI.onModelChanged(() => {
        reportPosition();
      });
      cleanupFunctions.push(cleanup);
    }

    // Listen for code language change
    if (typeof window.electronAPI?.onCodeLanguageChanged === 'function') {
      const cleanup = window.electronAPI.onCodeLanguageChanged(() => {
        reportPosition();
      });
      cleanupFunctions.push(cleanup);
    }
    
    // 监听DPI变化
    const mediaQueryList = window.matchMedia(`(resolution: ${window.devicePixelRatio}dppx)`);
    const handlePixelRatioChange = () => {
      // 当DPI变化时，强制重新计算位置
      setLastReportedBounds(null);
      reportPosition();
    };
    
    // 现代浏览器
    if (typeof mediaQueryList.addEventListener === 'function') {
      mediaQueryList.addEventListener('change', handlePixelRatioChange);
    }
    // 旧版浏览器
    else if (typeof mediaQueryList.addListener === 'function') {
      mediaQueryList.addListener(handlePixelRatioChange);
    }
    
    // Cleanup function
    return () => {
      window.removeEventListener('resize', handleResize);

      if (resizeTimeout) {
        clearTimeout(resizeTimeout);
      }

      // 清理语音视图的定期检查定时器
      if (intervalId) {
        clearInterval(intervalId);
        console.log('🎤 语音视图 - 停止定期位置检查');
      }

      // Clean up all event listeners
      cleanupFunctions.forEach(cleanup => cleanup());

      // 清理DPI变化监听器
      if (typeof mediaQueryList.removeEventListener === 'function') {
        mediaQueryList.removeEventListener('change', handlePixelRatioChange);
      } else if (typeof mediaQueryList.removeListener === 'function') {
        mediaQueryList.removeListener(handlePixelRatioChange);
      }
    };
  }, [modelSelector, codeLanguageSelector, resetSelector, captureSelector, solutionSelector, voiceSelector, errorState, currentView]);
  
  return (
    <div ref={elementRef} style={{ position: 'relative' }}>
      {children}
    </div>
  );
};

export default ToolbarPositionReporter;