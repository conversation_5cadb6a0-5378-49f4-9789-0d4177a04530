/**
 * RealtimeVoiceRecognition.ts
 * 实时语音识别管理器 - 整合麦克风和系统音频的实时识别功能
 * 
 * 主要功能：
 * 1. 统一管理麦克风和系统音频的实时识别
 * 2. 智能处理静音状态和音频质量检测
 * 3. 优化的文案收集和格式化逻辑
 * 4. 支持多音频源的同步识别
 */

import { BrowserWindow } from 'electron';
import { EventEmitter } from 'events';

// 音频源类型
export type AudioSource = 'microphone' | 'system';

// 音频数据接口
export interface AudioData {
  audio_data: Uint8Array | ArrayBuffer;
  sample_rate: number;
  audio_format: 'pcm';
  source: AudioSource;
  timestamp: number;
}

// 转写结果接口
export interface TranscriptionResult {
  text: string;
  isFinal: boolean;
  source: AudioSource;
  timestamp: string;
  confidence?: number;
  startTime?: number;
  endTime?: number;
  paragraphId?: string;
}

// 音频质量分析结果
export interface AudioQuality {
  rms: number;
  dynamicRange: number;
  snrEstimate: number;
  hasValidSignal: boolean;
  signalStrength: 'weak' | 'normal' | 'strong';
  isSilent: boolean;
}

// 语音识别配置
export interface VoiceConfig {
  voiceAppId: string;
  voiceAccessKeyId: string;
  enableMicrophone: boolean;
  enableSystemAudio: boolean;
  silenceThreshold: number;
  noiseGateThreshold: number;
  maxBufferSize: number;
  minBufferSize: number;
}

/**
 * 实时语音识别管理器
 */
export class RealtimeVoiceRecognition extends EventEmitter {
  private mainWindow: BrowserWindow | null = null;
  private config: VoiceConfig | null = null;
  private isActive: boolean = false;
  
  // 音频缓冲区管理
  private audioBuffers: Map<AudioSource, {
    buffer: Buffer[];
    size: number;
    lastActivity: number;
  }> = new Map();
  
  // 转写历史记录
  private transcriptionHistory: Array<{
    text: string;
    source: AudioSource;
    timestamp: Date;
    isFinal: boolean;
    paragraphId?: string;
  }> = [];
  
  // 实时转写状态
  private realtimeTranscripts: Map<AudioSource, string> = new Map();
  
  // 静音检测状态
  private silenceState: Map<AudioSource, {
    isSilent: boolean;
    silenceStartTime: number;
    consecutiveSilentChunks: number;
  }> = new Map();
  
  // 性能监控
  private performanceMetrics = {
    totalAudioProcessed: 0,
    totalTranscriptions: 0,
    averageLatency: 0,
    silentChunksFiltered: 0,
    lastResetTime: Date.now()
  };

  constructor() {
    super();
    this.initializeAudioBuffers();
    this.initializeSilenceState();
    this.startPerformanceMonitoring();
  }

  /**
   * 设置主窗口引用
   */
  public setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
    console.log('RealtimeVoiceRecognition: 主窗口已设置');
  }

  /**
   * 设置语音配置
   */
  public setConfig(config: VoiceConfig): void {
    this.config = config;
    console.log('RealtimeVoiceRecognition: 配置已更新', config);
  }

  /**
   * 启动实时语音识别
   */
  public async startRecognition(): Promise<{ success: boolean; error?: string }> {
    if (!this.config) {
      return { success: false, error: '语音配置未设置' };
    }

    if (this.isActive) {
      console.log('RealtimeVoiceRecognition: 语音识别已在运行中');
      return { success: true };
    }

    console.log('RealtimeVoiceRecognition: 启动实时语音识别');
    
    try {
      this.isActive = true;
      this.clearBuffers();
      this.resetSilenceState();
      
      // 通知UI状态变化
      this.notifyStatusChange('started');
      
      return { success: true };
    } catch (error) {
      console.error('RealtimeVoiceRecognition: 启动失败', error);
      this.isActive = false;
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '启动失败' 
      };
    }
  }

  /**
   * 停止实时语音识别
   */
  public async stopRecognition(): Promise<void> {
    if (!this.isActive) {
      console.log('RealtimeVoiceRecognition: 语音识别未在运行');
      return;
    }

    console.log('RealtimeVoiceRecognition: 停止实时语音识别');
    
    this.isActive = false;
    this.clearBuffers();
    this.resetSilenceState();
    
    // 通知UI状态变化
    this.notifyStatusChange('stopped');
  }

  /**
   * 处理音频数据
   */
  public async processAudioData(audioData: AudioData): Promise<void> {
    if (!this.isActive) {
      return;
    }

    const { source, audio_data, sample_rate, timestamp } = audioData;
    
    // 转换音频数据为Buffer
    const audioBuffer = audio_data instanceof ArrayBuffer 
      ? Buffer.from(audio_data) 
      : Buffer.from(audio_data);

    // 音频质量分析
    const quality = this.analyzeAudioQuality(audioBuffer, source);
    
    // 更新性能指标
    this.performanceMetrics.totalAudioProcessed += audioBuffer.length;
    
    // 静音检测和处理
    if (quality.isSilent) {
      this.handleSilentAudio(source, timestamp);
      this.performanceMetrics.silentChunksFiltered++;
      return;
    }

    // 处理有效音频
    await this.handleValidAudio(audioBuffer, source, sample_rate, timestamp, quality);
  }

  /**
   * 收集并格式化所有音频源的转写文案
   * 这是点击"请求AI"时调用的核心方法
   */
  public collectAndFormatTranscriptions(): string {
    console.log('🔄 开始收集并格式化所有音频源的文案...');

    // 创建包含所有文案的数组
    const allTranscriptions: Array<{
      text: string;
      timestamp: Date;
      source: AudioSource;
      type: 'history' | 'realtime';
    }> = [];

    // 1. 添加历史记录中的文案
    this.transcriptionHistory.forEach(item => {
      if (item.text.trim()) {
        allTranscriptions.push({
          text: item.text.trim(),
          timestamp: item.timestamp,
          source: item.source,
          type: 'history'
        });
      }
    });

    // 2. 添加当前实时识别的文案
    this.realtimeTranscripts.forEach((transcript, source) => {
      if (transcript.trim()) {
        allTranscriptions.push({
          text: transcript.trim(),
          timestamp: new Date(),
          source,
          type: 'realtime'
        });
      }
    });

    // 3. 按时间排序
    allTranscriptions.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    // 4. 按角色分组并格式化
    const systemTexts: string[] = [];
    const microphoneTexts: string[] = [];

    allTranscriptions.forEach(item => {
      if (item.source === 'system') {
        systemTexts.push(item.text);
      } else if (item.source === 'microphone') {
        microphoneTexts.push(item.text);
      }
    });

    // 5. 合并同类文案并限制长度
    const systemContent = systemTexts.join('').slice(-150); // 系统音频最近150字符
    const microphoneContent = microphoneTexts.join('').slice(-50); // 麦克风音频最近50字符

    // 6. 格式化最终文案
    let formattedText = '';
    if (systemContent) {
      formattedText += `提问方：${systemContent}；`;
    }
    if (microphoneContent) {
      formattedText += `回复方：${microphoneContent}；`;
    }

    console.log('📝 格式化完成:', {
      totalItems: allTranscriptions.length,
      systemTexts: systemTexts.length,
      microphoneTexts: microphoneTexts.length,
      systemContentLength: systemContent.length,
      microphoneContentLength: microphoneContent.length,
      finalText: formattedText
    });

    return formattedText;
  }

  /**
   * 获取当前识别状态
   */
  public getStatus(): {
    isActive: boolean;
    microphoneTranscript: string;
    systemTranscript: string;
    historyCount: number;
    performanceMetrics: typeof this.performanceMetrics;
  } {
    return {
      isActive: this.isActive,
      microphoneTranscript: this.realtimeTranscripts.get('microphone') || '',
      systemTranscript: this.realtimeTranscripts.get('system') || '',
      historyCount: this.transcriptionHistory.length,
      performanceMetrics: { ...this.performanceMetrics }
    };
  }

  /**
   * 初始化音频缓冲区
   */
  private initializeAudioBuffers(): void {
    const sources: AudioSource[] = ['microphone', 'system'];
    sources.forEach(source => {
      this.audioBuffers.set(source, {
        buffer: [],
        size: 0,
        lastActivity: 0
      });
    });
  }

  /**
   * 初始化静音状态
   */
  private initializeSilenceState(): void {
    const sources: AudioSource[] = ['microphone', 'system'];
    sources.forEach(source => {
      this.silenceState.set(source, {
        isSilent: false,
        silenceStartTime: 0,
        consecutiveSilentChunks: 0
      });
    });
  }

  /**
   * 音频质量分析
   */
  private analyzeAudioQuality(audioBuffer: Buffer, source: AudioSource): AudioQuality {
    // 转换为16位有符号样本
    const samples = new Int16Array(audioBuffer.length / 2);
    for (let i = 0; i < samples.length; i++) {
      let sample = (audioBuffer[i * 2] & 0xff) | ((audioBuffer[i * 2 + 1] & 0xff) << 8);
      if (sample > 32767) sample -= 65536;
      samples[i] = sample;
    }

    // 计算RMS
    let sumSquares = 0;
    let max = 0;
    let min = 0;
    let nonZeroCount = 0;

    for (let i = 0; i < samples.length; i++) {
      const sample = samples[i];
      sumSquares += sample * sample;
      max = Math.max(max, Math.abs(sample));
      min = Math.min(min, Math.abs(sample));
      if (sample !== 0) nonZeroCount++;
    }

    const rms = Math.sqrt(sumSquares / samples.length) / 32768;
    const dynamicRange = max > 0 ? 20 * Math.log10(max / Math.max(min, 1)) : 0;
    const snrEstimate = max > min ? 20 * Math.log10(max / Math.max(min, 1)) : 0;
    const nonZeroRatio = nonZeroCount / samples.length;

    // 根据音频源调整静音阈值
    const silenceThreshold = source === 'microphone' ? 0.005 : 0.003;
    const isSilent = rms < silenceThreshold || nonZeroRatio < 0.01;

    const hasValidSignal = (
      rms > silenceThreshold &&
      nonZeroRatio > 0.01 &&
      dynamicRange > 6 &&
      snrEstimate > 3
    );

    let signalStrength: 'weak' | 'normal' | 'strong' = 'weak';
    if (rms > 0.05) signalStrength = 'strong';
    else if (rms > 0.02) signalStrength = 'normal';

    return {
      rms,
      dynamicRange,
      snrEstimate,
      hasValidSignal,
      signalStrength,
      isSilent
    };
  }

  /**
   * 处理静音音频
   */
  private handleSilentAudio(source: AudioSource, timestamp: number): void {
    const silenceState = this.silenceState.get(source);
    if (!silenceState) return;

    if (!silenceState.isSilent) {
      // 刚开始静音
      silenceState.isSilent = true;
      silenceState.silenceStartTime = timestamp;
      silenceState.consecutiveSilentChunks = 1;

      console.log(`🔇 ${source} 开始静音`);

      // 通知UI静音状态变化
      this.notifyTranscriptionUpdate({
        text: '',
        isFinal: false,
        source,
        timestamp: new Date().toISOString(),
        paragraphId: `${source}-silence-${timestamp}`
      });
    } else {
      // 继续静音
      silenceState.consecutiveSilentChunks++;

      // 如果静音时间过长，清空实时转写
      const silenceDuration = timestamp - silenceState.silenceStartTime;
      if (silenceDuration > 5000) { // 5秒静音后清空
        this.realtimeTranscripts.set(source, '');
        console.log(`🔇 ${source} 长时间静音，清空实时转写`);
      }
    }
  }

  /**
   * 处理有效音频
   */
  private async handleValidAudio(
    audioBuffer: Buffer,
    source: AudioSource,
    sampleRate: number,
    timestamp: number,
    quality: AudioQuality
  ): Promise<void> {
    const silenceState = this.silenceState.get(source);
    if (silenceState?.isSilent) {
      // 从静音状态恢复
      silenceState.isSilent = false;
      silenceState.consecutiveSilentChunks = 0;
      console.log(`🔊 ${source} 从静音状态恢复，信号强度: ${quality.signalStrength}`);
    }

    // 更新缓冲区
    const buffer = this.audioBuffers.get(source);
    if (buffer) {
      buffer.buffer.push(audioBuffer);
      buffer.size += audioBuffer.length;
      buffer.lastActivity = timestamp;

      // 检查是否需要处理缓冲区
      const maxBufferSize = this.config?.maxBufferSize || 32000;
      if (buffer.size >= maxBufferSize) {
        await this.processBufferedAudio(source, sampleRate);
      }
    }
  }

  /**
   * 处理缓冲的音频数据
   */
  private async processBufferedAudio(source: AudioSource, sampleRate: number): Promise<void> {
    const buffer = this.audioBuffers.get(source);
    if (!buffer || buffer.buffer.length === 0) return;

    // 合并缓冲区中的音频数据
    const combinedBuffer = Buffer.concat(buffer.buffer);

    // 清空缓冲区
    buffer.buffer = [];
    buffer.size = 0;

    console.log(`🎵 处理 ${source} 缓冲音频: ${combinedBuffer.length} 字节`);

    // 这里应该调用实际的ASR服务
    // 为了演示，我们模拟一个转写结果
    await this.simulateASRProcessing(combinedBuffer, source, sampleRate);
  }

  /**
   * 模拟ASR处理（实际应用中应该调用真实的ASR服务）
   */
  private async simulateASRProcessing(
    audioBuffer: Buffer,
    source: AudioSource,
    sampleRate: number
  ): Promise<void> {
    // 模拟处理延迟
    await new Promise(resolve => setTimeout(resolve, 100));

    // 模拟转写结果
    const mockTexts = {
      microphone: ['好的', '我明白了', '请稍等', '让我想想', '是的'],
      system: ['请问', '你好', '帮我', '如何', '什么是']
    };

    const texts = mockTexts[source];
    const randomText = texts[Math.floor(Math.random() * texts.length)];

    // 模拟临时结果
    this.handleTranscriptionResult({
      text: randomText,
      isFinal: false,
      source,
      timestamp: new Date().toISOString(),
      confidence: 0.8,
      paragraphId: `${source}-${Date.now()}`
    });

    // 延迟后发送最终结果
    setTimeout(() => {
      this.handleTranscriptionResult({
        text: randomText + '完整版',
        isFinal: true,
        source,
        timestamp: new Date().toISOString(),
        confidence: 0.95,
        paragraphId: `${source}-${Date.now()}`
      });
    }, 500);
  }

  /**
   * 处理转写结果
   */
  private handleTranscriptionResult(result: TranscriptionResult): void {
    console.log(`📝 收到 ${result.source} 转写结果:`, result);

    if (result.isFinal) {
      // 最终结果：添加到历史记录
      this.transcriptionHistory.push({
        text: result.text,
        source: result.source,
        timestamp: new Date(result.timestamp),
        isFinal: true,
        paragraphId: result.paragraphId
      });

      // 限制历史记录长度
      if (this.transcriptionHistory.length > 100) {
        this.transcriptionHistory = this.transcriptionHistory.slice(-100);
      }

      // 清空对应的实时转写
      this.realtimeTranscripts.set(result.source, '');

      this.performanceMetrics.totalTranscriptions++;
    } else {
      // 临时结果：更新实时转写
      this.realtimeTranscripts.set(result.source, result.text);
    }

    // 通知UI更新
    this.notifyTranscriptionUpdate(result);
  }

  /**
   * 通知UI转写更新
   */
  private notifyTranscriptionUpdate(result: TranscriptionResult): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('asr:transcription', result);
    }

    // 发出事件
    this.emit('transcription', result);
  }

  /**
   * 通知UI状态变化
   */
  private notifyStatusChange(status: 'started' | 'stopped' | 'error'): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('voice-recognition:status', {
        status,
        isActive: this.isActive,
        timestamp: new Date().toISOString()
      });
    }

    // 发出事件
    this.emit('statusChange', { status, isActive: this.isActive });
  }

  /**
   * 清空所有缓冲区
   */
  private clearBuffers(): void {
    this.audioBuffers.forEach(buffer => {
      buffer.buffer = [];
      buffer.size = 0;
      buffer.lastActivity = 0;
    });

    this.realtimeTranscripts.clear();
    console.log('RealtimeVoiceRecognition: 缓冲区已清空');
  }

  /**
   * 重置静音状态
   */
  private resetSilenceState(): void {
    this.silenceState.forEach(state => {
      state.isSilent = false;
      state.silenceStartTime = 0;
      state.consecutiveSilentChunks = 0;
    });

    console.log('RealtimeVoiceRecognition: 静音状态已重置');
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    // 每30秒输出一次性能报告
    setInterval(() => {
      this.generatePerformanceReport();
    }, 30000);
  }

  /**
   * 生成性能报告
   */
  private generatePerformanceReport(): void {
    const now = Date.now();
    const runningTime = (now - this.performanceMetrics.lastResetTime) / 1000;

    console.log('=== RealtimeVoiceRecognition 性能报告 ===');
    console.log(`运行时间: ${runningTime.toFixed(1)} 秒`);
    console.log(`处理音频总量: ${(this.performanceMetrics.totalAudioProcessed / 1024 / 1024).toFixed(2)} MB`);
    console.log(`转写总数: ${this.performanceMetrics.totalTranscriptions}`);
    console.log(`过滤静音块: ${this.performanceMetrics.silentChunksFiltered}`);
    console.log(`历史记录数: ${this.transcriptionHistory.length}`);
    console.log(`当前状态: ${this.isActive ? '活跃' : '停止'}`);
    console.log('==========================================');

    // 发送性能报告到UI
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('voice-recognition:performance', {
        runningTime: runningTime.toFixed(1),
        totalAudioProcessed: (this.performanceMetrics.totalAudioProcessed / 1024 / 1024).toFixed(2),
        totalTranscriptions: this.performanceMetrics.totalTranscriptions,
        silentChunksFiltered: this.performanceMetrics.silentChunksFiltered,
        historyCount: this.transcriptionHistory.length,
        isActive: this.isActive,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    console.log('RealtimeVoiceRecognition: 开始清理资源');

    this.isActive = false;
    this.clearBuffers();
    this.resetSilenceState();
    this.transcriptionHistory = [];

    // 移除所有事件监听器
    this.removeAllListeners();

    console.log('RealtimeVoiceRecognition: 资源清理完成');
  }
}
