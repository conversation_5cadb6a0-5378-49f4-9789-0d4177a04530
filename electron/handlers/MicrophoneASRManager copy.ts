/**
 * MicrophoneASRManager.ts
 * Dedicated ASR Manager for Microphone Audio
 * Handles WebSocket connections specifically for microphone audio processing
 */

import { BrowserWindow } from 'electron';
import { getVoiceConfig, isVoiceEnabled } from '../store';
import { generateUUID } from '../utils';
import { unifiedHistoryManager, HistoryItem } from './UnifiedHistoryManager';

// Import WebSocket with error handling
let WebSocket: any;
try {
  WebSocket = require('ws');
  console.log('WebSocket module loaded successfully for MicrophoneASR');
} catch (error) {
  console.error('Failed to load ws module for MicrophoneASR:', error);
  WebSocket = class MockWebSocket {
    constructor() {
      throw new Error('WebSocket module is not available');
    }
  };
}

// Constants for WebSocket protocol
const PROTOCOL_VERSION = 0x01;
const DEFAULT_HEADER_SIZE = 0x01;
const FULL_CLIENT_REQUEST = 0x01;
const AUDIO_ONLY_REQUEST = 0x02;
const FULL_SERVER_RESPONSE = 0x09;
const SERVER_ACK = 0x0B;
const SERVER_ERROR_RESPONSE = 0x0F;
const POS_SEQUENCE = 0x01;
const JSON_FORMAT = 0x01;
const GZIP = 0x01;

export class MicrophoneASRManager {
  private wsConnection: any = null;
  private isConnected: boolean = false;
  private isProcessing: boolean = false;
  private mainWindow: BrowserWindow | null = null;
  private static instance: MicrophoneASRManager | null = null;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private messageQueue: Array<any> = [];
  private sessionStarted: boolean = false;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private lastActivityTimestamp: number = 0;
  private sequence: number = 1;
  private readyToSendAudio: boolean = false;
  private isConnecting: boolean = false;
  private config: any = null; // 存储语音配置信息
  private currentConnectionId: string = '';
  private isScheduledForClosure: boolean = false;
  private lastConnectionTime: number = 0;
  private connectionAttemptBlocked: boolean = false;
  private connectionBlockTimeout: NodeJS.Timeout | null = null;
  private readonly MIN_CONNECTION_INTERVAL = 30000; // 30秒
  private autoCloseTimer: NodeJS.Timeout | null = null;
  private lastPongTime: number = 0;
  private pingInterval: NodeJS.Timeout | null = null;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private lastAudioDataTime: number = 0;
  private audioTimeoutCheckTimer: NodeJS.Timeout | null = null;
  private readonly AUDIO_TIMEOUT_MS = 30000; // 增加到30秒无音频数据则自动关闭
  private silenceStartTime: number = 0; // 静音开始时间
  private isSilentPeriod: boolean = false; // 是否处于静音期
  private readonly MAX_SILENCE_DURATION = 120000; // 最大静音持续时间（2分钟）
  private silenceCheckTimer: NodeJS.Timeout | null = null;

  private static lastSendTime: number = 0;

  // 音频缓冲相关
  private audioBuffer: Buffer[] = [];
  private audioBufferSize = 0;
  private readonly MAX_BUFFER_SIZE = 8192; // 最大缓冲区大小（字节）
  private readonly MIN_BUFFER_SIZE = 2048; // 最小缓冲区大小（字节）

  private static lastTranscriptions: {
    finalTexts: Set<string>;
    lastInterimText: string;
    lastFinalText: string;
    lastTimestamp: number;
    lastHistoryUpdateTime: number;
    history: Array<{
      text: string;
      source: 'microphone';
      timestamp: string;
      isFinal: boolean;
      paragraphId?: string;
    }>;
  } = {
    finalTexts: new Set<string>(),
    lastInterimText: '',
    lastFinalText: '',
    lastTimestamp: 0,
    lastHistoryUpdateTime: 0,
    history: []
  };

  constructor() {
    console.log('MicrophoneASRManager instance created');
    // this.loadConfig();
    this.startAudioTimeoutCheck();
  }

  /**
   * 设置主窗口引用
   */
  public setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
    // 设置统一历史管理器的主窗口
    unifiedHistoryManager.setMainWindow(window);
  }

  /**
   * 从 store 中加载语音配置
   */
  private loadConfig(): void {
    try {
      this.config = getVoiceConfig();
      console.log('MicrophoneASRManager: 语音配置加载成功', this.config);
    } catch (error) {
      console.error('MicrophoneASRManager: 加载语音配置失败', error);
      this.config = null;
    }
  }

  /**
   * 检查麦克风音频识别功能是否启用
   */
  public isMicrophoneASREnabled(): boolean {
    const enabled = isVoiceEnabled();
    console.log('MicrophoneASRManager: 麦克风音频识别启用状态:', enabled);
    return enabled;
  }

  /**
   * 获取语音配置中的 AppId
   */
  public getVoiceAppId(): string {
    return this.config?.voiceAppId || '';
  }

  /**
   * 获取语音配置中的 AccessKeyId
   */
  public getVoiceAccessKeyId(): string {
    return this.config?.voiceAccessKeyId || '';
  }

  public static getInstance(): MicrophoneASRManager {
    if (!MicrophoneASRManager.instance) {
      MicrophoneASRManager.instance = new MicrophoneASRManager();
    }
    return MicrophoneASRManager.instance;
  }

  public async startASRSession(): Promise<{ success: boolean, error?: string }> {
    console.log('🎤 Starting MicrophoneASR session for microphone audio');

    if (!this.config) {
      this.loadConfig();
    }

    // 检查语音功能是否启用
    if (!this.isMicrophoneASREnabled()) {
      console.log('🚫 MicrophoneASR: 语音功能已禁用');
      return { success: false, error: '语音功能已禁用，请检查配置' };
    }

    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
      console.log('⏰ MicrophoneASR: 自动关闭计时器已取消');
    }

    if (this.isConnecting) {
      console.log('⏳ MicrophoneASR: Already attempting to connect');
      return { success: false, error: 'Connection already in progress' };
    }

    if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN && this.isConnected) {
      console.log('✅ MicrophoneASR: Active WebSocket connection already exists');
      return { success: true };
    }

    const now = Date.now();
    if (now - this.lastConnectionTime < this.MIN_CONNECTION_INTERVAL && this.connectionAttemptBlocked) {
      const remainingTime = Math.ceil((this.MIN_CONNECTION_INTERVAL - (now - this.lastConnectionTime)) / 1000);
      return { success: false, error: `Please wait ${remainingTime}s before reconnecting` };
    }

    if (this.wsConnection) {
      console.log('MicrophoneASR: Closing existing connection');
      try {
        this.isConnected = false;
        this.sessionStarted = false;
        
        if (this.wsConnection.readyState === WebSocket.OPEN) {
          this.wsConnection.close(1000, 'Closing before new connection');
        } else {
          this.wsConnection.terminate();
        }
      } catch (err) {
        console.error('MicrophoneASR: Error closing existing connection:', err);
      }
      
      this.wsConnection = null;
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    this.lastConnectionTime = now;
    this.messageQueue = [];
    this.reconnectAttempts = 0;
    this.sessionStarted = false;
    this.lastActivityTimestamp = Date.now();
    this.lastPongTime = Date.now();
    this.currentConnectionId = generateUUID();
    
    this.clearIntervals();
    
    try {
      await this.connectToASRService();
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown connection error' 
      };
    }
  }

  private clearIntervals(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  private async connectToASRService(): Promise<void> {
    this.isConnecting = true;
    const connectionId = this.currentConnectionId;
    
    return new Promise((resolve, reject) => {
      try {
        console.log(`MicrophoneASR: Starting connection attempt (ID: ${connectionId})`);
        
        const connectId = generateUUID();
        const targetUrl = 'wss://openspeech.bytedance.com/api/v3/sauc/bigmodel';
        
        const options = {
          headers: {
            'X-Api-App-Key': this.getVoiceAppId(),
            'X-Api-Access-Key': this.getVoiceAccessKeyId(),
            'X-Api-Resource-Id': 'volc.bigasr.sauc.duration',
            'X-Api-Connect-Id': connectId
          },
          rejectUnauthorized: false,
          protocolVersion: 13
        };
        
        this.wsConnection = new WebSocket(targetUrl, options);
        this.wsConnection.binaryType = 'arraybuffer';
        
        const connectionTimeout = setTimeout(() => {
          if (connectionId !== this.currentConnectionId) return;
          
          if (!this.isConnected) {
            console.error('MicrophoneASR: Connection timeout');
            if (this.wsConnection) {
              this.wsConnection.terminate();
              this.wsConnection = null;
            }
            
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
              this.reconnectAttempts++;
              console.log(`MicrophoneASR: Reconnection attempt ${this.reconnectAttempts} of ${this.maxReconnectAttempts}`);
              setTimeout(() => {
                if (connectionId === this.currentConnectionId) {
                  this.connectToASRService().then(resolve).catch(reject);
                }
              }, 3000);
            } else {
              this.isConnecting = false;
              reject(new Error('Connection timeout after multiple attempts'));
            }
          }
        }, 15000);
        
        this.wsConnection.on('open', () => {
          if (connectionId !== this.currentConnectionId) {
            clearTimeout(connectionTimeout);
            try {
              this.wsConnection.close(1000, "Superseded by newer connection");
            } catch (e) {
              console.error("MicrophoneASR: Error closing superseded connection:", e);
            }
            return;
          }
          
          clearTimeout(connectionTimeout);
          console.log('MicrophoneASR: WebSocket connection established');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.lastActivityTimestamp = Date.now();
          this.isConnecting = false;
          
          this.sendInitMessage();
          this.processQueuedMessages();
          this.startHeartbeat();
          
          if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.mainWindow.webContents.send('asr:connection-status', { connected: true });
          }
          
          resolve();
        });
        
        this.wsConnection.on('message', (message: any) => {
          if (connectionId !== this.currentConnectionId) return;
          this.handleWebSocketMessage(message);
        });
        
        this.wsConnection.on('error', (error: any) => {
          if (connectionId !== this.currentConnectionId) {
            clearTimeout(connectionTimeout);
            return;
          }
          
          clearTimeout(connectionTimeout);
          console.error('MicrophoneASR: WebSocket error:', error);

          this.isConnected = false;
          this.readyToSendAudio = false;
          this.sessionStarted = false;
          this.clearIntervals();

          // 清空音频缓冲区避免数据积压
          this.audioBuffer = [];
          this.audioBufferSize = 0;

          if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.mainWindow.webContents.send('asr:error', {
              error: true,
              message: `MicrophoneASR service error: ${error.message || 'Unknown error'}`,
              code: error.code || 1011,
              reconnectAttempts: this.reconnectAttempts,
              maxReconnectAttempts: this.maxReconnectAttempts
            });
          }

          // 智能重连策略：指数退避算法
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const backoffDelay = Math.min(1000 * Math.pow(2, this.reconnectAttempts - 1), 30000); // 最大30秒
            console.log(`MicrophoneASR: 将在${backoffDelay}ms后尝试第${this.reconnectAttempts}次重连`);

            setTimeout(() => {
              if (connectionId === this.currentConnectionId) {
                this.connectToASRService().then(resolve).catch(reject);
              }
            }, backoffDelay);
          } else {
            this.isConnecting = false;
            console.error('MicrophoneASR: 已达到最大重连次数，停止重连');
            reject(error);
          }
        });
        
        this.wsConnection.on('close', (code: number, reason: string) => {
          if (connectionId !== this.currentConnectionId) {
            clearTimeout(connectionTimeout);
            return;
          }
          
          clearTimeout(connectionTimeout);
          console.log(`MicrophoneASR: WebSocket closed with code ${code}: ${reason || 'No reason provided'}`);
          
          this.isConnected = false;
          this.sessionStarted = false;
          this.clearIntervals();
          
          if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.mainWindow.webContents.send('asr:connection-status', { 
              connected: false,
              code,
              reason
            });
          }
          
          if (code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
              if (connectionId === this.currentConnectionId) {
                this.connectToASRService().then(resolve).catch(reject);
              }
            }, 5000);
          } else {
            this.isConnecting = false;
          }
        });
      } catch (error) {
        console.error('MicrophoneASR: Failed to create WebSocket connection:', error);
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  private async sendInitMessage(): Promise<void> {
    if (!this.wsConnection || this.wsConnection.readyState !== WebSocket.OPEN) {
      console.error('MicrophoneASR: Cannot send init message: WebSocket not open');
      return;
    }

    try {
      const user = { uid: "microphone-audio" };
      const audio = {
        format: "pcm",
        sample_rate: 16000,
        bits: 16,
        channel: 1,
        codec: "raw"
      };
      
      const request = {
        model_name: "bigmodel",
        enable_punc: false,
        mode: "2pass-fast",
        enable_partial_result: true,
        enable_itn: true,
        latency: 1,
        refresh_ms: 250  // 更快的刷新率，适合实时麦克风输入
      };
      
      const payload = { user, audio, request };
      
      console.log('MicrophoneASR: 发送初始化消息');
      
      const payloadStr = JSON.stringify(payload);
      const compressedPayload = await this.compressData(payloadStr);
      const compressedData = new Uint8Array(compressedPayload);
      
      const header = this.createHeader(FULL_CLIENT_REQUEST, POS_SEQUENCE, JSON_FORMAT, GZIP, 0);
      const payloadSize = this.intToBytes(compressedData.length);
      this.sequence = 1;
      const sequenceBytes = this.intToBytes(this.sequence);
      
      const totalSize = header.length + sequenceBytes.length + payloadSize.length + compressedData.length;
      const message = new Uint8Array(totalSize);
      
      let offset = 0;
      message.set(header, offset);
      offset += header.length;
      message.set(sequenceBytes, offset);
      offset += sequenceBytes.length;
      message.set(payloadSize, offset);
      offset += payloadSize.length;
      message.set(compressedData, offset);
      
      this.wsConnection.send(message);
      this.readyToSendAudio = false;
      this.lastActivityTimestamp = Date.now();
    } catch (error) {
      console.error('MicrophoneASR: 发送初始化消息时出错:', error);
    }
  }

  private processQueuedMessages(): void {
    if (this.messageQueue.length > 0) {
      console.log(`MicrophoneASR: Sending ${this.messageQueue.length} queued messages`);
      this.messageQueue.forEach(msg => {
        if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
          this.wsConnection.send(msg);
        }
      });
      this.messageQueue = [];
    }
  }

  private startHeartbeat(): void {
    this.clearIntervals();
    
    this.pingInterval = setInterval(() => {
      if (this.isScheduledForClosure) {
        this.clearIntervals();
        return;
      }
      
      if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
        try {
          this.wsConnection.ping();
          
          const now = Date.now();
          if (now - this.lastPongTime > 30000) {
            console.warn('MicrophoneASR: No pong response for 30 seconds');
            this.handleConnectionFailure();
          }
        } catch (error) {
          console.error('MicrophoneASR: Error sending ping:', error);
          this.handleConnectionFailure();
        }
      }
    }, 15000);
    
    if (this.wsConnection) {
      this.wsConnection.on('pong', () => {
        this.lastPongTime = Date.now();
      });
    }
    
    this.heartbeatInterval = setInterval(() => {
      if (this.isScheduledForClosure) {
        this.clearIntervals();
        return;
      }
      
      if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
        const currentTime = Date.now();
        if (currentTime - this.lastActivityTimestamp > 15000) {
          console.log('MicrophoneASR: Sending heartbeat');
          
          try {
            this.wsConnection.send(JSON.stringify({ type: 'heartbeat' }));
            this.lastActivityTimestamp = currentTime;
          } catch (error) {
            console.error('MicrophoneASR: Error sending heartbeat:', error);
            this.handleConnectionFailure();
          }
        }
      } else {
        this.clearIntervals();
      }
    }, 10000);
  }

  public async processAudioData(audioData: any): Promise<void> {
    this.lastAudioDataTime = Date.now();

    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }

    this.lastActivityTimestamp = Date.now();

    const audioDataLength = audioData.audio_data ? audioData.audio_data.byteLength : 0;
    const sampleRate = audioData.sample_rate || 16000;
    console.log(`🎵 MicrophoneASR: ${Date.now()} 处理麦克风音频数据，长度=${audioDataLength}字节, 采样率=${sampleRate}Hz`);

    if (!this.wsConnection) {
      console.error('❌ MicrophoneASR: WebSocket连接未初始化');
      throw new Error('MicrophoneASR WebSocket connection not initialized');
    }

    console.log('🔗 MicrophoneASR: WebSocket状态:', {
      readyState: this.wsConnection.readyState,
      isConnected: this.isConnected,
      sessionStarted: this.sessionStarted,
      readyToSendAudio: this.readyToSendAudio
    });

    if (!this.sessionStarted && this.isConnected) {
      console.log(`⏳ MicrophoneASR: 会话尚未开始，将音频数据加入队列`);
      this.messageQueue.push(audioData);
      return;
    }
    
    const now = Date.now();
    const MIN_SEND_INTERVAL = 100; // 优化：减少发送间隔提高响应速度

    if (!MicrophoneASRManager.lastSendTime) {
      MicrophoneASRManager.lastSendTime = 0;
    }

    // 使用缓冲策略而不是简单跳过
    if (now - MicrophoneASRManager.lastSendTime < MIN_SEND_INTERVAL) {
      this.bufferAudioData(audioData);
      return;
    }

    // 检查是否有缓冲的数据需要先发送
    if (this.audioBufferSize > 0) {
      this.flushAudioBuffer(sampleRate);
    }
    
    if (this.wsConnection.readyState === WebSocket.OPEN && this.readyToSendAudio) {
      try {
        this.sequence++;
        MicrophoneASRManager.lastSendTime = now;
        
        const audioDataBytes = new Uint8Array(audioData.audio_data);
        
        if (!audioDataBytes || audioDataBytes.length === 0) {
          console.warn(`MicrophoneASR: 收到空的音频数据，跳过处理`);
          return;
        }
        
        // 改进的音频质量分析
        const audioQuality = this.analyzeAudioQuality(audioDataBytes);

        console.log(`MicrophoneASR: 音频质量分析 - RMS: ${audioQuality.rms.toFixed(4)}, 动态范围: ${audioQuality.dynamicRange.toFixed(2)}dB, 信噪比估计: ${audioQuality.snrEstimate.toFixed(2)}dB, 能量级别: ${audioQuality.energyLevel.toFixed(2)}, 静音状态: ${audioQuality.isSilent}, 有效信号: ${audioQuality.hasValidSignal}`);

        // 静音期管理
        if (audioQuality.isSilent) {
          if (!this.isSilentPeriod) {
            // 开始静音期
            this.isSilentPeriod = true;
            this.silenceStartTime = now;
            console.log('MicrophoneASR: 开始静音期');
            this.startSilenceCheck();
          }
          console.log('MicrophoneASR: 检测到静音，跳过发送');
          return;
        } else {
          if (this.isSilentPeriod) {
            // 结束静音期
            this.isSilentPeriod = false;
            const silenceDuration = now - this.silenceStartTime;
            console.log(`MicrophoneASR: 结束静音期，持续时间: ${silenceDuration}ms`);
            this.stopSilenceCheck();
            
            // 如果静音时间过长，可能需要重新建立连接
            if (silenceDuration > this.MAX_SILENCE_DURATION) {
              console.log('MicrophoneASR: 静音时间过长，准备重新建立连接');
              await this.restartConnection();
              return;
            }
          }
        }

        if (!audioQuality.hasValidSignal) {
          console.log('MicrophoneASR: 音频质量不足，跳过发送');
          return;
        }
        
        let processedAudioData = audioDataBytes;
        
        if (sampleRate !== 16000) {
          processedAudioData = this.convertSampleRate(audioDataBytes, sampleRate, 16000);
        }
        
        if (processedAudioData.length % 2 !== 0) {
          processedAudioData = processedAudioData.slice(0, processedAudioData.length - 1);
        }
        
        const compressedAudio = await this.compressData(processedAudioData);
        const compressedData = new Uint8Array(compressedAudio);
        
        if (!compressedData || compressedData.length === 0) {
          console.error(`MicrophoneASR: 音频数据压缩失败，跳过发送`);
          return;
        }
        
        const header = this.createHeader(AUDIO_ONLY_REQUEST, POS_SEQUENCE, JSON_FORMAT, GZIP, 0);
        const sequenceBytes = this.intToBytes(this.sequence);
        const payloadSize = this.intToBytes(compressedData.length);
        
        const totalSize = header.length + sequenceBytes.length + payloadSize.length + compressedData.length;
        const message = new Uint8Array(totalSize);
        
        let offset = 0;
        message.set(header, offset);
        offset += header.length;
        message.set(sequenceBytes, offset);
        offset += sequenceBytes.length;
        message.set(payloadSize, offset);
        offset += payloadSize.length;
        message.set(compressedData, offset);
        
        console.log(`MicrophoneASR: 发送音频数据，序列号=${this.sequence}, 总大小=${totalSize}字节, 原始长度=${audioDataLength}字节, 压缩率=${(compressedData.length / processedAudioData.length * 100).toFixed(2)}%`);
        this.wsConnection.send(message);
        
        this.lastActivityTimestamp = Date.now();
        this.isProcessing = true;
        
      } catch (error) {
        console.error(`MicrophoneASR: 发送音频数据出错:`, error);
        throw error;
      }
    } else if (this.wsConnection.readyState === WebSocket.OPEN && !this.readyToSendAudio) {
      console.warn('MicrophoneASR: WebSocket已连接但尚未准备好发送音频，加入队列');
      this.messageQueue.push(audioData);
    } else {
      console.warn(`MicrophoneASR: WebSocket未连接，音频数据加入队列`);
      this.messageQueue.push(audioData);
    }
  }

  // Helper methods (optimized for microphone with improved sample rate conversion)
  private convertSampleRate(audioData: Uint8Array, sourceSampleRate: number, targetSampleRate: number): Uint8Array {
    if (sourceSampleRate === targetSampleRate) {
      return audioData;
    }

    const dataLength = audioData.length % 2 === 0 ? audioData.length : audioData.length - 1;
    const samples = new Int16Array(dataLength / 2);

    // 转换字节数组为16位样本，使用小端序
    for (let i = 0; i < samples.length; i++) {
      samples[i] = (audioData[i * 2] & 0xff) | ((audioData[i * 2 + 1] & 0xff) << 8);
      // 处理有符号16位整数
      if (samples[i] > 32767) {
        samples[i] -= 65536;
      }
    }

    const ratio = targetSampleRate / sourceSampleRate;
    const newSampleCount = Math.floor(samples.length * ratio);
    const newSamples = new Int16Array(newSampleCount);

    // 使用改进的重采样算法 - Lanczos插值（简化版）
    for (let i = 0; i < newSampleCount; i++) {
      const srcIndex = i / ratio;
      const srcIndexInt = Math.floor(srcIndex);
      const srcIndexFrac = srcIndex - srcIndexInt;

      if (srcIndexInt >= samples.length - 1) {
        // 边界处理：使用最后一个样本
        newSamples[i] = samples[samples.length - 1];
        continue;
      }

      // 使用三次插值提高音频质量
      let result: number;
      if (srcIndexInt === 0 || srcIndexInt >= samples.length - 2) {
        // 边界情况使用线性插值
        result = samples[srcIndexInt] * (1 - srcIndexFrac) + samples[srcIndexInt + 1] * srcIndexFrac;
      } else {
        // 三次插值（Catmull-Rom样条）
        const p0 = samples[srcIndexInt - 1];
        const p1 = samples[srcIndexInt];
        const p2 = samples[srcIndexInt + 1];
        const p3 = samples[srcIndexInt + 2];

        const t = srcIndexFrac;
        const t2 = t * t;
        const t3 = t2 * t;

        result = 0.5 * (
          (2 * p1) +
          (-p0 + p2) * t +
          (2 * p0 - 5 * p1 + 4 * p2 - p3) * t2 +
          (-p0 + 3 * p1 - 3 * p2 + p3) * t3
        );
      }

      // 限制在16位有符号整数范围内
      newSamples[i] = Math.max(-32768, Math.min(32767, Math.round(result)));
    }

    // 转换回字节数组
    const newAudioData = new Uint8Array(newSamples.length * 2);
    for (let i = 0; i < newSamples.length; i++) {
      const sample = newSamples[i];
      // 转换为无符号16位用于字节存储
      const unsignedSample = sample < 0 ? sample + 65536 : sample;
      newAudioData[i * 2] = unsignedSample & 0xff;
      newAudioData[i * 2 + 1] = (unsignedSample >> 8) & 0xff;
    }

    console.log(`MicrophoneASR: 采样率转换 ${sourceSampleRate}Hz -> ${targetSampleRate}Hz, 样本数 ${samples.length} -> ${newSamples.length}`);
    return newAudioData;
  }

  /**
   * 改进的音频质量分析方法
   * 综合考虑RMS、动态范围、信噪比等多个指标，增强静音检测
   */
  private analyzeAudioQuality(audioData: Uint8Array): {
    rms: number;
    dynamicRange: number;
    snrEstimate: number;
    hasValidSignal: boolean;
    signalStrength: 'weak' | 'normal' | 'strong';
    isSilent: boolean;
    energyLevel: number;
  } {
    // 转换为16位有符号样本进行分析
    const samples = new Int16Array(audioData.length / 2);
    for (let i = 0; i < samples.length; i++) {
      let sample = (audioData[i * 2] & 0xff) | ((audioData[i * 2 + 1] & 0xff) << 8);
      if (sample > 32767) sample -= 65536;
      samples[i] = sample;
    }

    // 计算基本统计信息
    let sumSquares = 0;
    let max = -32768;
    let min = 32767;
    let nonZeroCount = 0;
    let absSum = 0; // 绝对值总和，用于计算平均幅度
    let activeSamples = 0; // 超过噪声阈值的样本数

    const NOISE_THRESHOLD = 100; // 噪声阈值，低于此值认为是背景噪声

    for (let i = 0; i < samples.length; i++) {
      const sample = samples[i];
      const absSample = Math.abs(sample);
      
      sumSquares += sample * sample;
      max = Math.max(max, absSample);
      min = Math.min(min, absSample);
      absSum += absSample;
      
      if (sample !== 0) nonZeroCount++;
      if (absSample > NOISE_THRESHOLD) activeSamples++;
    }

    const rms = Math.sqrt(sumSquares / samples.length) / 32768; // 归一化到0-1
    const averageAmplitude = absSum / samples.length;
    const energyLevel = sumSquares / samples.length; // 能量级别
    const dynamicRange = max > 0 ? 20 * Math.log10(max / Math.max(min, 1)) : 0;

    // 估算信噪比：使用最小值作为噪声基线
    const noiseFloor = min;
    const signalPeak = max;
    const snrEstimate = signalPeak > noiseFloor ? 20 * Math.log10(signalPeak / Math.max(noiseFloor, 1)) : 0;

    // 非零样本比例和活跃样本比例
    const nonZeroRatio = nonZeroCount / samples.length;
    const activeSampleRatio = activeSamples / samples.length;

    // 增强的静音检测
    const SILENCE_RMS_THRESHOLD = 0.005; // 提高RMS阈值
    const SILENCE_ENERGY_THRESHOLD = 1000000; // 能量阈值
    const SILENCE_ACTIVE_RATIO_THRESHOLD = 0.05; // 活跃样本比例阈值
    const SILENCE_AVG_AMPLITUDE_THRESHOLD = 200; // 平均幅度阈值

    const isSilent = (
      rms < SILENCE_RMS_THRESHOLD ||
      energyLevel < SILENCE_ENERGY_THRESHOLD ||
      activeSampleRatio < SILENCE_ACTIVE_RATIO_THRESHOLD ||
      averageAmplitude < SILENCE_AVG_AMPLITUDE_THRESHOLD ||
      max < 500 // 最大幅度阈值
    );

    // 综合判断是否为有效语音信号（更严格的条件）
    const hasValidSignal = (
      !isSilent &&
      rms > 0.008 &&                    // 提高RMS阈值
      nonZeroRatio > 0.3 &&             // 提高非零样本比例要求
      activeSampleRatio > 0.1 &&        // 至少10%的样本超过噪声阈值
      dynamicRange > 12 &&              // 提高动态范围要求到12dB
      snrEstimate > 6 &&                // 提高信噪比要求到6dB
      averageAmplitude > 300            // 平均幅度要求
    );

    // 信号强度分级（基于更精确的指标）
    let signalStrength: 'weak' | 'normal' | 'strong';
    if (rms < 0.02 || averageAmplitude < 500) {
      signalStrength = 'weak';
    } else if (rms < 0.08 || averageAmplitude < 2000) {
      signalStrength = 'normal';
    } else {
      signalStrength = 'strong';
    }

    return {
      rms,
      dynamicRange,
      snrEstimate,
      hasValidSignal,
      signalStrength,
      isSilent,
      energyLevel: energyLevel / 1000000 // 归一化能量级别
    };
  }

  /**
   * 缓存音频数据进行批量处理
   */
  private bufferAudioData(audioData: any): void {
    if (!audioData.audio_data || audioData.audio_data.length === 0) {
      return;
    }

    const audioBuffer = Buffer.from(audioData.audio_data);
    this.audioBuffer.push(audioBuffer);
    this.audioBufferSize += audioBuffer.length;

    // 当缓冲区达到最小大小或超过最大大小时，立即处理
    if (this.audioBufferSize >= this.MIN_BUFFER_SIZE || this.audioBufferSize >= this.MAX_BUFFER_SIZE) {
      this.flushAudioBuffer(audioData.sample_rate || 16000);
    }
  }

  /**
   * 清空音频缓冲区并发送数据
   */
  private flushAudioBuffer(sampleRate: number = 16000): void {
    if (this.audioBuffer.length === 0 || this.audioBufferSize === 0) {
      return;
    }

    // 合并所有缓冲的音频数据
    const combinedBuffer = Buffer.concat(this.audioBuffer, this.audioBufferSize);

    // 创建合并后的音频数据对象
    const combinedAudioData = {
      audio_data: new Uint8Array(combinedBuffer),
      sample_rate: sampleRate,
      audio_format: 'pcm'
    };

    // 重置缓冲区
    this.audioBuffer = [];
    this.audioBufferSize = 0;

    // 发送合并后的数据
    console.log(`MicrophoneASR: 发送缓冲的音频数据，大小=${combinedBuffer.length}字节`);
    this.processCombinedAudioData(combinedAudioData).catch(error => {
      console.error('MicrophoneASR: 发送缓冲音频数据失败:', error);
    });
  }

  /**
   * 处理合并后的音频数据
   */
  private async processCombinedAudioData(audioData: any): Promise<void> {
    const sampleRate = audioData.sample_rate || 16000;
    const audioDataBytes = new Uint8Array(audioData.audio_data);

    // 进行音频质量分析
    const audioQuality = this.analyzeAudioQuality(audioDataBytes);

    console.log(`MicrophoneASR: 缓冲音频质量分析 - RMS: ${audioQuality.rms.toFixed(4)}, 静音状态: ${audioQuality.isSilent}, 有效信号: ${audioQuality.hasValidSignal}`);

    // 静音检测：如果检测到静音，直接跳过发送
    if (audioQuality.isSilent) {
      console.log('MicrophoneASR: 缓冲的音频为静音，跳过发送');
      return;
    }

    if (!audioQuality.hasValidSignal) {
      console.log('MicrophoneASR: 缓冲的音频质量不足，跳过发送');
      return;
    }

    // 直接发送到ASR服务
    await this.sendAudioDataToASR(audioDataBytes, sampleRate);
  }

  /**
   * 发送音频数据到ASR服务的核心方法
   */
  private async sendAudioDataToASR(audioDataBytes: Uint8Array, sampleRate: number): Promise<void> {
    // 🔍 数据验证：确保音频数据有效
    if (!audioDataBytes || audioDataBytes.length === 0) {
      console.warn('MicrophoneASR: 音频数据为空，跳过发送');
      return;
    }

    if (audioDataBytes.length < 320) { // 至少10ms的16kHz音频数据
      console.warn(`MicrophoneASR: 音频数据过短(${audioDataBytes.length}字节)，跳过发送`);
      return;
    }

    let processedAudioData = audioDataBytes;

    if (sampleRate !== 16000) {
      console.log(`MicrophoneASR: 转换采样率 ${sampleRate}Hz -> 16000Hz`);
      processedAudioData = this.convertSampleRate(audioDataBytes, sampleRate, 16000);
    }

    if (processedAudioData.length % 2 !== 0) {
      processedAudioData = processedAudioData.slice(0, processedAudioData.length - 1);
      console.log('MicrophoneASR: 调整音频数据长度为偶数');
    }

    // 🔍 验证音频数据格式
    console.log(`MicrophoneASR: 准备发送音频数据 - 长度: ${processedAudioData.length}字节, 采样率: 16000Hz, 格式: PCM16`);

    // 检查音频数据的前几个字节，确保不是JSON或其他文本格式
    const firstBytes = Array.from(processedAudioData.slice(0, Math.min(8, processedAudioData.length)));
    const isTextData = firstBytes.every(byte => byte >= 32 && byte <= 126); // ASCII可打印字符范围

    if (isTextData && processedAudioData.length > 8) {
      console.error('MicrophoneASR: 检测到文本数据而非音频数据，停止发送');
      console.error('MicrophoneASR: 数据前8字节:', firstBytes.map(b => String.fromCharCode(b)).join(''));
      return;
    }

    const compressedData = await this.compressData(processedAudioData);
    const audioDataLength = processedAudioData.length;

    this.sequence++;
    MicrophoneASRManager.lastSendTime = Date.now();

    // 🔧 修复：使用正确的音频数据消息头
    const header = this.createHeader(AUDIO_ONLY_REQUEST, POS_SEQUENCE, JSON_FORMAT, GZIP, 0);
    const sequenceBytes = this.intToBytes(this.sequence);
    const payloadSize = this.intToBytes(compressedData.byteLength);

    const totalSize = header.length + sequenceBytes.length + payloadSize.length + compressedData.byteLength;
    const message = new Uint8Array(totalSize);

    let offset = 0;
    message.set(header, offset);
    offset += header.length;
    message.set(sequenceBytes, offset);
    offset += sequenceBytes.length;
    message.set(payloadSize, offset);
    offset += payloadSize.length;
    message.set(new Uint8Array(compressedData), offset);

    console.log(`MicrophoneASR: 发送音频数据，序列号=${this.sequence}, 总大小=${totalSize}字节, 原始长度=${audioDataLength}字节, 压缩率=${(compressedData.byteLength / processedAudioData.length * 100).toFixed(2)}%`);
    console.log(`MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)`);

    this.wsConnection.send(message);

    this.lastActivityTimestamp = Date.now();
    this.isProcessing = true;
  }

  private async compressData(data: string | Uint8Array | ArrayBuffer): Promise<ArrayBufferLike> {
    try {
      const zlib = require('zlib');
      const { promisify } = require('util');
      const gzipAsync = promisify(zlib.gzip);
      
      let inputData: Buffer;
      
      if (typeof data === 'string') {
        inputData = Buffer.from(data);
      } else if (data instanceof Uint8Array) {
        inputData = Buffer.from(data);
      } else if (data instanceof ArrayBuffer) {
        inputData = Buffer.from(data);
      } else {
        throw new Error('Unsupported data type for compression');
      }
      
      try {
        const compressed = await gzipAsync(inputData, { level: zlib.constants.Z_BEST_SPEED });
        return compressed.buffer.slice(compressed.byteOffset, compressed.byteOffset + compressed.byteLength);
      } catch (gzipError) {
        console.error('MicrophoneASR: Error compressing data:', gzipError);
        return inputData.buffer.slice(inputData.byteOffset, inputData.byteOffset + inputData.byteLength);
      }
    } catch (error) {
      console.error('MicrophoneASR: Error importing zlib:', error);
      
      if (typeof data === 'string') {
        return new TextEncoder().encode(data).buffer;
      } else if (data instanceof ArrayBuffer) {
        return data;
      } else if (data instanceof Uint8Array) {
        return data.buffer;
      } else {
        throw new Error('Unsupported data type for compression');
      }
    }
  }

  private async decompressData(data: Uint8Array): Promise<Uint8Array> {
    try {
      const zlib = require('zlib');
      const { promisify } = require('util');
      const gunzipAsync = promisify(zlib.gunzip);
      
      try {
        const decompressed = await gunzipAsync(data);
        return new Uint8Array(decompressed);
      } catch (gunzipError) {
        console.warn('MicrophoneASR: gunzip failed, data may not be compressed:', gunzipError);
        return data;
      }
    } catch (error) {
      console.error('MicrophoneASR: Error importing zlib for decompression:', error);
      return data;
    }
  }

  private createHeader(messageType: number, messageTypeSpecificFlags: number, serializationMethod: number, compressionType: number, reservedData: number): Uint8Array {
    const header = new Uint8Array(4);
    header[0] = (PROTOCOL_VERSION << 4) | DEFAULT_HEADER_SIZE;
    header[1] = (messageType << 4) | messageTypeSpecificFlags;
    header[2] = (serializationMethod << 4) | compressionType;
    header[3] = reservedData;
    return header;
  }

  private intToBytes(value: number): Uint8Array {
    const bytes = new Uint8Array(4);
    bytes[0] = (value >> 24) & 0xFF;
    bytes[1] = (value >> 16) & 0xFF;
    bytes[2] = (value >> 8) & 0xFF;
    bytes[3] = value & 0xFF;
    return bytes;
  }

  private bytesToInt(bytes: Uint8Array): number {
    if (bytes.length !== 4) {
      throw new Error('Bytes to int conversion requires exactly 4 bytes');
    }
    return ((bytes[0] & 0xFF) << 24) |
           ((bytes[1] & 0xFF) << 16) |
           ((bytes[2] & 0xFF) << 8) |
           (bytes[3] & 0xFF);
  }

  private handleWebSocketMessage(data: any): void {
    if (data instanceof Blob) {
      const reader = new FileReader();
      reader.onload = () => {
        this.parseResponse(new Uint8Array(reader.result as ArrayBuffer));
      };
      reader.readAsArrayBuffer(data);
    } else if (data instanceof ArrayBuffer) {
      this.parseResponse(new Uint8Array(data));
    } else if (typeof data === 'string') {
      console.error('MicrophoneASR: Error from server:', data);
      this.notifyError(data);
    }
  }

  private async parseResponse(data: Uint8Array): Promise<number> {
    if (!data || data.length === 0) {
      console.warn('MicrophoneASR: Empty response data');
      return -1;
    }
    
    if (data.length < 12) {
      try {
        const textDecoder = new TextDecoder();
        const text = textDecoder.decode(data);
        console.log('MicrophoneASR: Small response (possibly error):', text);
        this.notifyError(text);
        return -1;
      } catch (e) {
        console.error('MicrophoneASR: Error decoding small response:', e);
      }
    }
    
    const protocolVersion = (data[0] >> 4) & 0x0F;
    const headerSize = data[0] & 0x0F;
    const messageType = (data[1] >> 4) & 0x0F;
    const messageTypeSpecificFlags = data[1] & 0x0F;
    const serializationMethod = (data[2] >> 4) & 0x0F;
    const compressionType = data[2] & 0x0F;
    const reserved = data[3];
    
    const sequenceBytes = data.slice(4, 8);
    const sequence = this.bytesToInt(sequenceBytes);
    
    const payloadSizeBytes = data.slice(8, 12);
    const payloadSize = this.bytesToInt(payloadSizeBytes);
    
    const payload = data.slice(12);
    
    let payloadStr: string = '';
    
    if (messageType === FULL_SERVER_RESPONSE) {
      console.log('MicrophoneASR: Received FULL_SERVER_RESPONSE');
      
      if (compressionType === GZIP) {
        const decompressedPayload = await this.decompressData(payload);
        payloadStr = new TextDecoder().decode(decompressedPayload);
        await this.handleTranscriptResponse(payloadStr);
      } else {
        payloadStr = new TextDecoder().decode(payload);
        await this.handleTranscriptResponse(payloadStr);
      }
      
      this.readyToSendAudio = true;
    } else if (messageType === SERVER_ACK) {
      console.log('MicrophoneASR: Received SERVER_ACK');
      
      if (payload.length > 0) {
        payloadStr = new TextDecoder().decode(payload);
        console.log('MicrophoneASR: Server ACK payload:', payloadStr);
      }
      
      this.readyToSendAudio = true;
    } else if (messageType === SERVER_ERROR_RESPONSE) {
      console.log('MicrophoneASR: Received SERVER_ERROR_RESPONSE');
      
      const errorCode = sequence;
      payloadStr = new TextDecoder().decode(payload);
      console.error(`MicrophoneASR: Server error (code ${errorCode}):`, payloadStr);
      this.notifyError(`Server error (${errorCode}): ${payloadStr}`);
      
      this.readyToSendAudio = false;
    } else {
      console.warn('MicrophoneASR: Unknown message type:', messageType);
    }
    
    return sequence;
  }

  private async handleTranscriptResponse(payloadStr: string): Promise<void> {
    try {
      if (this.isScheduledForClosure) {
        console.log('MicrophoneASR: Connection scheduled for closure, ignoring transcript response');
        return;
      }
      
      console.log('MicrophoneASR: Processing transcript response:', payloadStr);
      
      const response = JSON.parse(payloadStr);
      
      if (response.error) {
        console.error('MicrophoneASR: Error in response:', response.error);
        this.notifyError(`MicrophoneASR Error: ${response.error.message || 'Unknown error'}`);
        return;
      }
      
      const logId = response?.result?.additions?.log_id;
      if (logId && !this.sessionStarted) {
        console.log('MicrophoneASR: New session started with ID:', logId);
        this.sessionStarted = true;
      }
      
      const now = Date.now();
      if (now - MicrophoneASRManager.lastTranscriptions.lastTimestamp > 15000) {
        console.log('MicrophoneASR: 距离上次转写超过15秒，清空临时记录');
        MicrophoneASRManager.lastTranscriptions.lastInterimText = '';
        MicrophoneASRManager.lastTranscriptions.lastFinalText = '';
        MicrophoneASRManager.lastTranscriptions.finalTexts.clear();
      }
      MicrophoneASRManager.lastTranscriptions.lastTimestamp = now;
      
      const paragraphId = `microphone-${Date.now()}`;
      let shouldUpdateHistory = false;
      
      if (response?.result?.utterances?.length > 0) {
        console.log(`MicrophoneASR: Processing ${response.result.utterances.length} utterances`);
        
        for (const utterance of response.result.utterances) {
          if (!utterance.text) continue;
          
          const text = utterance.text.trim();
          if (text.length === 0) continue;
          
          const isFinal = utterance.definite === true;
          const startTimeMs = utterance.start_time || 0;
          const endTimeMs = utterance.end_time || 0;
          
          if (isFinal) {
            // 检查是否与最近的历史记录重复或包含
            let isRedundant = false;
            const recentHistory = MicrophoneASRManager.lastTranscriptions.history.slice(-5);
            
            for (const historyItem of recentHistory) {
              // 检查完全相同
              if (historyItem.text === text) {
                console.log(`MicrophoneASR: 跳过完全相同的文本: "${text}"`);
                isRedundant = true;
                break;
              }
              
              // 检查包含关系
              if (historyItem.text.includes(text) || text.includes(historyItem.text)) {
                const shorterText = historyItem.text.length < text.length ? historyItem.text : text;
                const longerText = historyItem.text.length >= text.length ? historyItem.text : text;
                
                // 如果短文本是长文本的一部分，并且长度超过长文本的70%
                if (longerText.includes(shorterText) && shorterText.length > longerText.length * 0.7) {
                  console.log(`MicrophoneASR: 检测到文本重叠: "${text}" 与 "${historyItem.text}"`);
                  
                  // 如果新文本更长，更新现有记录
                  if (text.length > historyItem.text.length) {
                    historyItem.text = text;
                    historyItem.timestamp = new Date().toISOString();
                    shouldUpdateHistory = true;
                  }
                  
                  isRedundant = true;
                  break;
                }
              }
            }
            
            if (!isRedundant) {
              MicrophoneASRManager.lastTranscriptions.lastFinalText = text;

              // 创建历史项并添加到统一历史管理器
              const historyItem: HistoryItem = {
                text,
                source: 'microphone' as const,
                timestamp: new Date().toISOString(),
                isFinal: true,
                paragraphId
              };

              // 使用统一历史管理器
              unifiedHistoryManager.addHistoryItem(historyItem);

              // 保持原有的本地历史记录（用于去重）- 创建兼容的本地历史项
              const localHistoryItem = {
                text,
                source: 'microphone' as const,
                timestamp: new Date().toISOString(),
                isFinal: true,
                paragraphId
              };
              MicrophoneASRManager.lastTranscriptions.history.push(localHistoryItem);

              // 限制历史记录大小
              if (MicrophoneASRManager.lastTranscriptions.history.length > 50) {
                MicrophoneASRManager.lastTranscriptions.history = MicrophoneASRManager.lastTranscriptions.history.slice(-50);
              }

              shouldUpdateHistory = true;
            }
          } else {
            // 处理临时结果
            if (text === MicrophoneASRManager.lastTranscriptions.lastInterimText) {
              console.log(`MicrophoneASR: 跳过重复的临时结果: "${text}"`);
              continue;
            }
            
            // 检查临时结果是否与最近的最终结果重复
            let isRedundant = false;
            for (const finalText of MicrophoneASRManager.lastTranscriptions.finalTexts) {
              if (finalText.includes(text) || text.includes(finalText)) {
                console.log(`MicrophoneASR: 临时结果与最终结果重复: "${text}"`);
                isRedundant = true;
                break;
              }
            }
            
            if (!isRedundant) {
              MicrophoneASRManager.lastTranscriptions.lastInterimText = text;
              
              // 发送临时结果到渲染进程
              if (this.mainWindow && !this.mainWindow.isDestroyed()) {
                const transcriptionData = {
                  text,
                  isFinal: false,
                  timestamp: new Date().toISOString(),
                  startTime: startTimeMs,
                  endTime: endTimeMs,
                  source: 'microphone',
                  paragraphId
                };
                
                this.mainWindow.webContents.send('asr:transcription', transcriptionData);
              }
            }
          }
        }
      }
      
      // 注意：历史记录更新现在由统一历史管理器处理
      // 这里不再需要单独发送历史更新事件
      
      this.readyToSendAudio = true;
      
      // 处理消息队列
      if (this.messageQueue.length > 0 && this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
        const nextAudioData = this.messageQueue.shift();
        if (nextAudioData) {
          this.processAudioData(nextAudioData).catch(err => {
            console.error(`MicrophoneASR: Error processing queued audio data:`, err);
          });
        }
      }
    } catch (error) {
      console.error('MicrophoneASR: Error parsing transcript response:', error);
      this.readyToSendAudio = true;
    }
  }

  private notifyError(message: string): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('asr:connection-status', { 
        connected: false,
        error: message
      });
      
      this.mainWindow.webContents.send('asr:error', { 
        message
      });
    }
  }

  public isASRConnected(): boolean {
    return this.isConnected && this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN;
  }

  public isASRProcessing(): boolean {
    return this.isProcessing;
  }

  public isConnectionBlocked(): boolean {
    return this.connectionAttemptBlocked;
  }

  public getCooldownTimeRemaining(): number {
    if (!this.connectionAttemptBlocked) return 0;
    
    const now = Date.now();
    const timeSinceLastConnection = now - this.lastConnectionTime;
    const remainingTime = Math.max(0, this.MIN_CONNECTION_INTERVAL - timeSinceLastConnection);
    
    return remainingTime;
  }

  private handleConnectionFailure(): void {
    console.log('MicrophoneASR: Handling connection failure...');
    
    // 停止静音检查
    this.stopSilenceCheck();
    this.isSilentPeriod = false;
    
    if (this.wsConnection) {
      try {
        this.wsConnection.terminate();
      } catch (e) {
        console.error('MicrophoneASR: Error terminating broken connection:', e);
      }
      this.wsConnection = null;
    }
    
    this.isConnected = false;
    this.clearIntervals();
    
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('asr:connection-status', { connected: false });
    }
    
    // 改进的重连策略：考虑是否为静音期间的连接失败
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
      }
      
      this.reconnectAttempts++;
      
      // 如果是在静音期间失败，延长重连间隔
      const baseDelay = 2000 * Math.pow(1.5, this.reconnectAttempts - 1);
      const silenceMultiplier = this.isSilentPeriod ? 2 : 1; // 静音期间延长重连间隔
      const delay = Math.min(baseDelay * silenceMultiplier, 30000);
      
      console.log(`MicrophoneASR: Scheduling reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms${this.isSilentPeriod ? ' (延长间隔，静音期间)' : ''}`);
      
      this.reconnectTimer = setTimeout(async () => {
        console.log('MicrophoneASR: Attempting to reconnect...');
        try {
          await this.connectToASRService();
          // 重连成功后重置重连计数
          this.reconnectAttempts = 0;
        } catch (error) {
          console.error('MicrophoneASR: Reconnection failed:', error);
          // 如果重连仍然失败，会在 connectToASRService 中继续重试
        }
      }, delay);
    } else {
      console.log('MicrophoneASR: 已达到最大重连次数，停止自动重连');
      // 重置重连计数，以便用户手动重新启动时可以再次尝试
      this.reconnectAttempts = 0;
    }
  }

  public stopASRSession(): void {
    console.log('MicrophoneASR: Stopping ASR session');
    
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }
    
    this.isScheduledForClosure = true;
    this.currentConnectionId = generateUUID();
    
    this.clearIntervals();
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.wsConnection) {
      console.log('MicrophoneASR: Stopping session');
      
      try {
        if (this.wsConnection.readyState === WebSocket.OPEN) {
          const endMessage = { type: 'end' };
          this.wsConnection.send(JSON.stringify(endMessage));
          
          setTimeout(() => {
            if (this.wsConnection) {
              this.wsConnection.close(1000, 'Session ended by client');
              this.wsConnection = null;
              this.isScheduledForClosure = false;
            }
          }, 500);
        } else {
          this.wsConnection.close(1000, 'Session ended by client');
          this.wsConnection = null;
          this.isScheduledForClosure = false;
        }
      } catch (error) {
        console.error('MicrophoneASR: Error stopping session:', error);
        
        if (this.wsConnection) {
          try {
            this.wsConnection.close();
          } catch (e) {
            console.error('MicrophoneASR: Error closing WebSocket:', e);
          }
          this.wsConnection = null;
        }
        this.isScheduledForClosure = false;
      }
    } else {
      this.isScheduledForClosure = false;
    }
    
    this.isConnected = false;
    this.isProcessing = false;
    this.sessionStarted = false;
    this.messageQueue = [];
    this.isConnecting = false;
  }

  /**
   * 开始静音检查
   */
  private startSilenceCheck(): void {
    if (this.silenceCheckTimer) {
      clearTimeout(this.silenceCheckTimer);
    }

    this.silenceCheckTimer = setTimeout(() => {
      if (this.isSilentPeriod) {
        const silenceDuration = Date.now() - this.silenceStartTime;
        if (silenceDuration > this.MAX_SILENCE_DURATION) {
          console.log(`MicrophoneASR: 静音时间超过${this.MAX_SILENCE_DURATION/1000}秒，准备关闭连接`);
          this.stopASRSession();
        }
      }
    }, this.MAX_SILENCE_DURATION);
  }

  /**
   * 停止静音检查
   */
  private stopSilenceCheck(): void {
    if (this.silenceCheckTimer) {
      clearTimeout(this.silenceCheckTimer);
      this.silenceCheckTimer = null;
    }
  }

  /**
   * 重新启动连接
   */
  private async restartConnection(): Promise<void> {
    console.log('MicrophoneASR: 重新启动连接...');
    
    // 停止当前连接
    this.stopASRSession();
    
    // 等待一段时间后重新连接
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    try {
      const result = await this.startASRSession();
      if (result.success) {
        console.log('MicrophoneASR: 连接重启成功');
      } else {
        console.error('MicrophoneASR: 连接重启失败:', result.error);
      }
    } catch (error) {
      console.error('MicrophoneASR: 连接重启异常:', error);
    }
  }

  public dispose(): void {
    console.log('MicrophoneASR: Disposing manager');
    
    this.lastAudioDataTime = 0;
    
    if (this.audioTimeoutCheckTimer) {
      clearInterval(this.audioTimeoutCheckTimer);
      this.audioTimeoutCheckTimer = null;
    }
    
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }

    if (this.silenceCheckTimer) {
      clearTimeout(this.silenceCheckTimer);
      this.silenceCheckTimer = null;
    }
    
    this.clearIntervals();
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.connectionBlockTimeout) {
      clearTimeout(this.connectionBlockTimeout);
      this.connectionBlockTimeout = null;
    }
    
    if (this.wsConnection) {
      try {
        this.wsConnection.terminate();
      } catch (e) {
        console.error('MicrophoneASR: Error terminating WebSocket during disposal:', e);
      }
      this.wsConnection = null;
    }
    
    this.isConnected = false;
    this.isProcessing = false;
    this.sessionStarted = false;
    this.isConnecting = false;
    this.isScheduledForClosure = false;
    this.isSilentPeriod = false;
  }

  private startAudioTimeoutCheck(): void {
    if (this.audioTimeoutCheckTimer) {
      clearInterval(this.audioTimeoutCheckTimer);
    }

    this.audioTimeoutCheckTimer = setInterval(() => {
      if (this.isConnected && this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
        const now = Date.now();
        if (now - this.lastAudioDataTime > this.AUDIO_TIMEOUT_MS && this.lastAudioDataTime > 0) {
          console.log(`MicrophoneASR: 超过${this.AUDIO_TIMEOUT_MS/1000}秒未接收到音频数据，准备关闭服务`);
          
          if (this.autoCloseTimer) {
            clearTimeout(this.autoCloseTimer);
          }
          
          this.autoCloseTimer = setTimeout(() => {
            console.log('MicrophoneASR: 由于音频数据超时，自动关闭会话');
            this.stopASRSession();
          }, 2000);
        }
      }
    }, 2000);
  }

  private looksLikeInstruction(text: string): boolean {
    const instructionKeywords = ['帮我', '请问', '怎么', '如何', '为什么', '什么是', '解决', '处理', '设置', '配置'];
    const lowerText = text.toLowerCase();
    return instructionKeywords.some(keyword => lowerText.includes(keyword));
  }

  /**
   * 去除历史记录中的重复项
   * 通过文本内容和来源进行去重，保留最新的记录
   */
  private deduplicateHistory<T extends { text: string; source: string; timestamp: string }>(history: T[]): T[] {
    // 使用Map按文本内容和来源分组，保留最新的记录
    const seen = new Map<string, T>();
    
    for (const item of history) {
      // 确保麦克风数据源始终为"microphone"
      if (item.source !== 'microphone') {
        (item as any).source = 'microphone';
      }
      
      // 使用文本和来源组合作为键，确保不同来源的相同文本被视为不同条目
      const key = `${item.source}:${item.text}`;
      
      // 如果是新记录或比已存在的记录更新，则保留
      if (!seen.has(key) || new Date(item.timestamp) > new Date(seen.get(key)!.timestamp)) {
        seen.set(key, item);
      }
    }
    
    // 根据时间戳排序，返回去重后的数组
    return Array.from(seen.values()).sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
  }
}

export const microphoneASRManager = MicrophoneASRManager.getInstance();
export default microphoneASRManager;