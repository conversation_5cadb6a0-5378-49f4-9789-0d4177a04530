import { ipcMain, screen } from "electron";
import { AppState } from "./main";
import { models, store, getCodeLanguages, getDefaultCodeLanguage } from "./store";

// Track which handlers have been registered
const registeredHandlers = new Set<string>();

/**
 * Safely register an IPC handler, removing any existing one first
 */
function safeRegisterHandler(channel: string, handler: (...args: any[]) => Promise<any> | any): void {
  if (registeredHandlers.has(channel)) {
    try {
      ipcMain.removeHandler(channel);
    } catch (error) {
      console.log(`Unable to remove existing handler for ${channel}:`, error);
    }
  }
  
  ipcMain.handle(channel, handler);
  registeredHandlers.add(channel);
}

export function initializeIpcHandlers(appState: AppState): void {
  const mainWindow = appState.getMainWindow();

  if (!mainWindow) {
    console.error("Main window is not available for IPC handlers");
    return;
  }

  // Clear any existing handlers
  for (const channel of registeredHandlers) {
    try {
      ipcMain.removeHandler(channel);
    } catch (error) {
      console.log(`Failed to remove existing handler for ${channel}:`, error);
    }
  }
  registeredHandlers.clear();

  // Window dimension update
  safeRegisterHandler(
    "update-content-dimensions",
    async (event, { width, height }: { width: number; height: number }) => {
      if (width && height) {
        appState.setWindowDimensions(width, height)
      }
    }
  );

  // Screenshot management
  safeRegisterHandler("delete-screenshot", async (event, path: string) => {
    return appState.deleteScreenshot(path)
  });

  // API key management
  safeRegisterHandler("get-api-key", async () => {
    return store.get("openaiApiKey")
  });

  // Model management
  safeRegisterHandler("get-current-model", async (event) => {
    const curModel = store.get("curModel") || models[0];
    const index = models.findIndex(model => model === curModel) + 1;

    if (event.sender) {
      event.sender.send("model-changed", {
        model: curModel,
        index,
        total: models.length
      });
    }

    return {
      model: curModel,
      index,
      total: models.length
    };
  });

  // Code language management
  safeRegisterHandler("get-current-code-language", async (event) => {
    const codeLanguages = getCodeLanguages();
    const curCodeLanguage = store.get("curCodeLanguage") || getDefaultCodeLanguage();
    const index = codeLanguages.findIndex(lang => lang === curCodeLanguage) + 1;

    if (event.sender) {
      event.sender.send("code-language-changed", {
        codeLanguage: curCodeLanguage,
        index,
        total: codeLanguages.length
      });
    }

    return {
      codeLanguage: curCodeLanguage,
      index,
      total: codeLanguages.length
    };
  });

  // Screenshot capture
  safeRegisterHandler("take-screenshot", async () => {
    try {
      const screenshotPath = await appState.takeScreenshot()
      const preview = await appState.getImagePreview(screenshotPath)
      return { path: screenshotPath, preview }
    } catch (error) {
      console.error("Error taking screenshot:", error)
      throw error
    }
  });

  safeRegisterHandler("take-full-screenshot", async () => {
    try {
      // Get primary display dimensions for full screen capture
      const primaryDisplay = screen.getPrimaryDisplay();
      const { width, height } = primaryDisplay.bounds;
      
      // Use the region screenshot method with full screen dimensions
      const screenshotPath = await appState.takeRegionScreenshot(0, 0, width, height);
      const preview = await appState.getImagePreview(screenshotPath);
      
      // Return the same data format as take-screenshot
      return { path: screenshotPath, preview };
    } catch (error) {
      console.error("Error taking full screenshot:", error);
      throw error;
    }
  });

  // Solution generation
  safeRegisterHandler("generate-solution", async () => {
    try {
      // Check if processing is already active
      if (appState.processingHelper.isProcessingActive()) {
        console.log("Already processing a request, ignoring solution generation");
        return { success: false, error: "Already processing a request" };
      }

      // Check if there are screenshots to process
      if (appState.getScreenshotQueue().length === 0) {
        console.log("No screenshots to process");
        // Notify the renderer that there are no screenshots to process
        const mainWindow = appState.getMainWindow();
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send("processing-no-screenshots"); 
        }
        return { success: false, error: "No screenshots to process" };
      }

      // Generate and process solutions using the chat model
      appState.processingHelper.generateAndProcessScreenshotSolutionsNew();
      return { success: true };
    } catch (error) {
      console.error("Error generating solution:", error);
      throw error;
    }
  });

  // Screenshot queue management
  safeRegisterHandler("get-screenshots", async () => {
    try {
      let previews = await Promise.all(
        appState.getScreenshotQueue().map(async (path) => ({
          path,
          preview: await appState.getImagePreview(path)
        }))
      )

      return previews
    } catch (error) {
      console.error("Error getting screenshots:", error)
      throw error
    }
  });

  // Window management
  safeRegisterHandler("toggle-window", async () => {
    appState.toggleMainWindow()
  });

  // Queue reset
  safeRegisterHandler("reset-queues", async () => {
    try {
      appState.clearQueues()

      return { success: true }
    } catch (error: any) {
      console.error("Error resetting queues:", error)
      return { success: false, error: error.message }
    }
  });

  // API key setting
  safeRegisterHandler("set-api-key", (_event, apiKey: string) => {
    try {
      store.set("openaiApiKey", apiKey)
      appState.setIgnoreMouseEvents(true)
      return { success: true }
    } catch (error) {
      console.error("Error setting API key:", error)
      return { success: false, error: "Failed to set API key" }
    }
  });

  // Toolbar management
  safeRegisterHandler(
    "update-toolbar-bounds",
    (_event, bounds: { x: number; y: number; width: number; height: number }) => {
      try {
        appState.mouseTrackingHelper.updateToolbarBounds(bounds);
        return { success: true };
      } catch (error) {
        console.error("更新工具条位置出错:", error);
        return { success: false, error: "Failed to update toolbar bounds" };
      }
    }
  );

  safeRegisterHandler(
    "update-toolbar-regions",
    (_event, regions: Array<{ x: number; y: number; width: number; height: number; type: 'model' | 'code-language' | 'reset' | 'capture' | 'solution' | 'other' | 'voice' }>) => {
      try {
        appState.mouseTrackingHelper.updateToolbarRegions(regions);
        return { success: true };
      } catch (error) {
        console.error("更新工具条区域出错:", error);
        return { success: false, error: "Failed to update toolbar regions" };
      }
    }
  );

  // AI回复框位置上报处理器
  safeRegisterHandler(
    "voice-ai-response-positions",
    (_event, positions: {
      fastResponse?: { x: number; y: number; width: number; height: number; type: string };
      accurateResponse?: { x: number; y: number; width: number; height: number; type: string };
    }) => {
      try {
        appState.mouseTrackingHelper.updateAIResponsePositions(positions);
        return { success: true };
      } catch (error) {
        console.error("更新AI回复框位置出错:", error);
        return { success: false, error: "Failed to update AI response positions" };
      }
    }
  );

  // AI回复框箭头按钮位置上报处理器
  safeRegisterHandler(
    "voice-ai-arrow-positions",
    (_event, positions: {
      [key: string]: { x: number; y: number; width: number; height: number; type: string };
    }) => {
      try {
        appState.mouseTrackingHelper.updateAIArrowPositions(positions);
        return { success: true };
      } catch (error) {
        console.error("更新AI箭头按钮位置出错:", error);
        return { success: false, error: "Failed to update AI arrow positions" };
      }
    }
  );

  // 注意：语音识别相关的处理器已经在 voiceHandlers.ts 中注册
  // 这里不再重复注册，避免 "Attempted to register a second handler" 错误

  /*
  // 已移除重复的语音处理器注册 - 现在在 voiceHandlers.ts 中处理
  safeRegisterHandler("voice-recognition:start-flow", async (_event, options: {
    enableSystemAudio?: boolean,
    enableMicrophone?: boolean
  } = {}) => {
    const { enableSystemAudio = true, enableMicrophone = false } = options;
    console.log(`收到启动语音识别流程请求: 系统音频=${enableSystemAudio}, 麦克风=${enableMicrophone}`);
    
    try {
      let systemAudioResult: { success: boolean; error?: string } = { success: true };
      let microphoneResult: { success: boolean; error?: string } = { success: true };
      
      // 独立启动系统音频
      if (enableSystemAudio) {
        console.log('启动系统音频识别...');
        systemAudioResult = await systemAudioManager.startCapturing();
        if (!systemAudioResult.success) {
          console.error('启动系统音频捕获失败:', systemAudioResult.error || '未知错误');
          // 不中断流程，继续处理麦克风
        }
      }
      
      // 独立启动麦克风
      if (enableMicrophone) {
        console.log('启动麦克风识别...');
        microphoneResult = await microphoneManager.startCapturing();
        if (!microphoneResult.success) {
          console.error('启动麦克风捕获失败:', microphoneResult.error || '未知错误');
        } else {
          // 通知渲染进程启动麦克风识别
          mainWindow.webContents.send('start-microphone-recognition');
        }
      }
      
      // 只要有一个成功就算成功
      const overallSuccess = systemAudioResult.success || microphoneResult.success;
      
      // 通知前端状态
      mainWindow.webContents.send('toggle-voice-assistant', {
        asrConnected: overallSuccess,
        recording: overallSuccess,
        status: overallSuccess ? 'active' : 'error',
        systemAudioActive: enableSystemAudio && systemAudioResult.success,
        microphoneActive: enableMicrophone && microphoneResult.success,
        error: overallSuccess ? undefined : '语音识别服务启动失败'
      });
      
      return { 
        success: overallSuccess,
        systemAudio: systemAudioResult,
        microphone: microphoneResult
      };
      
    } catch (error) {
      console.error('启动语音识别流程失败:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '启动语音识别流程失败' 
      };
    }
  });
  
  // 一键启动语音识别处理器（兼容性保留）
  safeRegisterHandler("voice-recognition:start", async (_event, startMicrophone = false) => {
    console.log(`一键启动语音识别服务${startMicrophone ? '（包含麦克风）' : ''}`);
    
    try {
      // 使用新的流程启动接口
      const result = await mainWindow.webContents.executeJavaScript(`
        window.electron.invoke('voice-recognition:start-flow', {
          enableSystemAudio: true,
          enableMicrophone: ${startMicrophone}
        })
      `);
      
      return result;
      
    } catch (error) {
      console.error('启动语音识别流程失败:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '启动语音识别流程失败' 
      };
    }
  });

  // 停止语音识别流程
  safeRegisterHandler("voice-recognition:stop", async (_event, options: {
    stopSystemAudio?: boolean,
    stopMicrophone?: boolean
  } = {}) => {
    const { stopSystemAudio = true, stopMicrophone = true } = options;
    console.log(`收到停止语音识别流程请求: 系统音频=${stopSystemAudio}, 麦克风=${stopMicrophone}`);
    
    try {
      // 并行停止服务
      const stopPromises: Promise<any>[] = [];
      
      if (stopSystemAudio) {
        stopPromises.push(systemAudioManager.stopCapturing().catch(err => {
          console.error('停止系统音频失败:', err);
          return { success: false, error: err.message };
        }));
      }
      
      if (stopMicrophone) {
        stopPromises.push(microphoneManager.stopCapturing().catch(err => {
          console.error('停止麦克风失败:', err);
          return { success: false, error: err.message };
        }));
      }
      
      await Promise.allSettled(stopPromises);
      
      // 通知前端状态
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('toggle-voice-assistant', {
          asrConnected: false,
          recording: false,
          status: 'inactive'
        });
      }
      
      return { success: true };
    } catch (error) {
      console.error('停止语音识别服务出错:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '停止语音识别服务失败'
      };
    }
  });
  */

  // 注意：系统音频相关的处理器已经在 systemAudioHandlers.ts 中注册
  // 这里不再重复注册，避免 "Attempted to register a second handler" 错误

  /*
  // 已移除重复的系统音频处理器注册 - 现在在 systemAudioHandlers.ts 中处理
  safeRegisterHandler('system-audio:start-capturing', ...);
  safeRegisterHandler('system-audio:stop-capturing', ...);
  */

  // 麦克风相关处理器
  // 注意：麦克风相关的处理器已经在 microphoneHandlers.ts 中注册
  // 这里不再重复注册，避免 "Attempted to register a second handler" 错误

  /*
  // 已移除重复的麦克风处理器注册 - 现在在 microphoneHandlers.ts 中处理
  safeRegisterHandler('microphone:start-capturing', ...);
  safeRegisterHandler('microphone:stop-capturing', ...);
  safeRegisterHandler('microphone:process-audio', ...);
  */

  safeRegisterHandler('system-audio:process-audio', async (_event, audioData: any) => {
    try {
      // SystemAudioManager处理音频数据是通过文件监控自动完成的
      // 这里只需要返回成功状态
      return { success: true };
    } catch (error: any) {
      console.error('Error processing system audio data:', error);
      return { success: false, error: error.message };
    }
  });


}
