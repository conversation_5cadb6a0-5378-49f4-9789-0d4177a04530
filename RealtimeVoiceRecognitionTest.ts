/**
 * RealtimeVoiceRecognitionTest.ts
 * 实时语音识别系统测试文件
 * 
 * 包含单元测试和集成测试，验证系统各个功能模块
 */

import { RealtimeVoiceRecognition, AudioData, TranscriptionResult, VoiceConfig } from './RealtimeVoiceRecognition';
import { VoiceRecognitionConfigManager, DEFAULT_CONFIG } from './VoiceRecognitionConfig';

/**
 * 模拟BrowserWindow用于测试
 */
class MockBrowserWindow {
  public isDestroyed = false;
  public webContents = {
    send: (channel: string, data: any) => {
      console.log(`Mock send: ${channel}`, data);
    }
  };
}

/**
 * 音频数据生成器 - 用于生成测试音频数据
 */
class AudioDataGenerator {
  /**
   * 生成静音音频数据
   */
  static generateSilentAudio(durationMs: number, sampleRate: number = 16000): ArrayBuffer {
    const samples = Math.floor(durationMs * sampleRate / 1000);
    const buffer = new ArrayBuffer(samples * 2); // 16位音频
    const view = new Int16Array(buffer);
    
    // 填充静音数据（全零或极小的随机噪声）
    for (let i = 0; i < samples; i++) {
      view[i] = Math.random() * 10 - 5; // 极小的噪声
    }
    
    return buffer;
  }
  
  /**
   * 生成有效音频数据（模拟语音）
   */
  static generateValidAudio(durationMs: number, sampleRate: number = 16000, amplitude: number = 8000): ArrayBuffer {
    const samples = Math.floor(durationMs * sampleRate / 1000);
    const buffer = new ArrayBuffer(samples * 2);
    const view = new Int16Array(buffer);
    
    // 生成正弦波模拟语音
    const frequency = 440; // A4音符
    for (let i = 0; i < samples; i++) {
      const t = i / sampleRate;
      const sample = Math.sin(2 * Math.PI * frequency * t) * amplitude;
      // 添加一些随机变化模拟语音特征
      const variation = (Math.random() - 0.5) * amplitude * 0.3;
      view[i] = Math.floor(sample + variation);
    }
    
    return buffer;
  }
  
  /**
   * 生成噪音音频数据
   */
  static generateNoiseAudio(durationMs: number, sampleRate: number = 16000, amplitude: number = 1000): ArrayBuffer {
    const samples = Math.floor(durationMs * sampleRate / 1000);
    const buffer = new ArrayBuffer(samples * 2);
    const view = new Int16Array(buffer);
    
    // 生成随机噪声
    for (let i = 0; i < samples; i++) {
      view[i] = Math.floor((Math.random() - 0.5) * amplitude * 2);
    }
    
    return buffer;
  }
}

/**
 * 测试套件
 */
export class RealtimeVoiceRecognitionTestSuite {
  private voiceRecognition: RealtimeVoiceRecognition;
  private mockWindow: MockBrowserWindow;
  private testResults: Array<{ name: string; passed: boolean; error?: string }> = [];
  
  constructor() {
    this.voiceRecognition = new RealtimeVoiceRecognition();
    this.mockWindow = new MockBrowserWindow();
  }
  
  /**
   * 运行所有测试
   */
  public async runAllTests(): Promise<void> {
    console.log('🧪 开始运行实时语音识别测试套件...');
    
    // 基础功能测试
    await this.testBasicInitialization();
    await this.testConfigurationManagement();
    await this.testAudioQualityAnalysis();
    await this.testSilenceDetection();
    await this.testTranscriptionFormatting();
    
    // 集成测试
    await this.testAudioProcessingFlow();
    await this.testMultiSourceProcessing();
    await this.testPerformanceMonitoring();
    
    // 输出测试结果
    this.printTestResults();
  }
  
  /**
   * 测试基础初始化
   */
  private async testBasicInitialization(): Promise<void> {
    try {
      console.log('🔧 测试基础初始化...');
      
      // 设置主窗口
      this.voiceRecognition.setMainWindow(this.mockWindow as any);
      
      // 设置配置
      const config: VoiceConfig = {
        voiceAppId: 'test-app-id',
        voiceAccessKeyId: 'test-access-key',
        enableMicrophone: true,
        enableSystemAudio: true,
        silenceThreshold: 0.005,
        noiseGateThreshold: 0.003,
        maxBufferSize: 32000,
        minBufferSize: 6400
      };
      
      this.voiceRecognition.setConfig(config);
      
      // 启动识别
      const result = await this.voiceRecognition.startRecognition();
      
      if (result.success) {
        this.testResults.push({ name: '基础初始化', passed: true });
        console.log('✅ 基础初始化测试通过');
      } else {
        throw new Error(result.error || '启动失败');
      }
      
    } catch (error) {
      this.testResults.push({ 
        name: '基础初始化', 
        passed: false, 
        error: error instanceof Error ? error.message : '未知错误' 
      });
      console.error('❌ 基础初始化测试失败:', error);
    }
  }
  
  /**
   * 测试配置管理
   */
  private async testConfigurationManagement(): Promise<void> {
    try {
      console.log('⚙️ 测试配置管理...');
      
      const configManager = new VoiceRecognitionConfigManager();
      
      // 测试默认配置
      const defaultConfig = configManager.getConfig();
      if (!defaultConfig.enableMicrophone || !defaultConfig.enableSystemAudio) {
        throw new Error('默认配置不正确');
      }
      
      // 测试预设配置
      configManager.applyPreset('highPerformance');
      const highPerfConfig = configManager.getConfig();
      if (highPerfConfig.asrService.mode !== '2pass-fast') {
        throw new Error('高性能预设配置应用失败');
      }
      
      // 测试配置验证
      const validation = configManager.validateConfig();
      if (!validation.isValid) {
        console.warn('配置验证警告:', validation.errors);
      }
      
      this.testResults.push({ name: '配置管理', passed: true });
      console.log('✅ 配置管理测试通过');
      
    } catch (error) {
      this.testResults.push({ 
        name: '配置管理', 
        passed: false, 
        error: error instanceof Error ? error.message : '未知错误' 
      });
      console.error('❌ 配置管理测试失败:', error);
    }
  }
  
  /**
   * 测试音频质量分析
   */
  private async testAudioQualityAnalysis(): Promise<void> {
    try {
      console.log('🎵 测试音频质量分析...');
      
      // 测试静音音频
      const silentAudio = AudioDataGenerator.generateSilentAudio(1000);
      const silentAudioData: AudioData = {
        audio_data: silentAudio,
        sample_rate: 16000,
        audio_format: 'pcm',
        source: 'microphone',
        timestamp: Date.now()
      };
      
      await this.voiceRecognition.processAudioData(silentAudioData);
      
      // 测试有效音频
      const validAudio = AudioDataGenerator.generateValidAudio(1000);
      const validAudioData: AudioData = {
        audio_data: validAudio,
        sample_rate: 16000,
        audio_format: 'pcm',
        source: 'microphone',
        timestamp: Date.now()
      };
      
      await this.voiceRecognition.processAudioData(validAudioData);
      
      // 测试噪音音频
      const noiseAudio = AudioDataGenerator.generateNoiseAudio(1000);
      const noiseAudioData: AudioData = {
        audio_data: noiseAudio,
        sample_rate: 16000,
        audio_format: 'pcm',
        source: 'microphone',
        timestamp: Date.now()
      };
      
      await this.voiceRecognition.processAudioData(noiseAudioData);
      
      this.testResults.push({ name: '音频质量分析', passed: true });
      console.log('✅ 音频质量分析测试通过');
      
    } catch (error) {
      this.testResults.push({ 
        name: '音频质量分析', 
        passed: false, 
        error: error instanceof Error ? error.message : '未知错误' 
      });
      console.error('❌ 音频质量分析测试失败:', error);
    }
  }
  
  /**
   * 测试静音检测
   */
  private async testSilenceDetection(): Promise<void> {
    try {
      console.log('🔇 测试静音检测...');
      
      // 连续发送静音音频，测试静音状态管理
      for (let i = 0; i < 5; i++) {
        const silentAudio = AudioDataGenerator.generateSilentAudio(500);
        const audioData: AudioData = {
          audio_data: silentAudio,
          sample_rate: 16000,
          audio_format: 'pcm',
          source: 'system',
          timestamp: Date.now() + i * 500
        };
        
        await this.voiceRecognition.processAudioData(audioData);
        await new Promise(resolve => setTimeout(resolve, 100)); // 短暂延迟
      }
      
      // 发送有效音频，测试从静音状态恢复
      const validAudio = AudioDataGenerator.generateValidAudio(1000);
      const validAudioData: AudioData = {
        audio_data: validAudio,
        sample_rate: 16000,
        audio_format: 'pcm',
        source: 'system',
        timestamp: Date.now()
      };
      
      await this.voiceRecognition.processAudioData(validAudioData);
      
      this.testResults.push({ name: '静音检测', passed: true });
      console.log('✅ 静音检测测试通过');
      
    } catch (error) {
      this.testResults.push({ 
        name: '静音检测', 
        passed: false, 
        error: error instanceof Error ? error.message : '未知错误' 
      });
      console.error('❌ 静音检测测试失败:', error);
    }
  }
  
  /**
   * 测试转写文案格式化
   */
  private async testTranscriptionFormatting(): Promise<void> {
    try {
      console.log('📝 测试转写文案格式化...');
      
      // 模拟添加一些转写历史
      const mockTranscriptions = [
        { text: '你好', source: 'system' as const, timestamp: new Date(), isFinal: true },
        { text: '我想问一个问题', source: 'system' as const, timestamp: new Date(), isFinal: true },
        { text: '好的', source: 'microphone' as const, timestamp: new Date(), isFinal: true },
        { text: '请说', source: 'microphone' as const, timestamp: new Date(), isFinal: true }
      ];
      
      // 通过私有方法访问（仅用于测试）
      (this.voiceRecognition as any).transcriptionHistory = mockTranscriptions;
      (this.voiceRecognition as any).realtimeTranscripts.set('system', '这是实时系统音频');
      (this.voiceRecognition as any).realtimeTranscripts.set('microphone', '这是实时麦克风音频');
      
      // 测试文案格式化
      const formattedText = this.voiceRecognition.collectAndFormatTranscriptions();
      
      console.log('格式化结果:', formattedText);
      
      // 验证格式化结果
      if (!formattedText.includes('提问方：') || !formattedText.includes('回复方：')) {
        throw new Error('文案格式化结果不正确');
      }
      
      this.testResults.push({ name: '转写文案格式化', passed: true });
      console.log('✅ 转写文案格式化测试通过');
      
    } catch (error) {
      this.testResults.push({ 
        name: '转写文案格式化', 
        passed: false, 
        error: error instanceof Error ? error.message : '未知错误' 
      });
      console.error('❌ 转写文案格式化测试失败:', error);
    }
  }
  
  /**
   * 测试音频处理流程
   */
  private async testAudioProcessingFlow(): Promise<void> {
    try {
      console.log('🔄 测试音频处理流程...');
      
      // 模拟完整的音频处理流程
      const audioSources = ['microphone', 'system'] as const;
      
      for (const source of audioSources) {
        // 发送多个音频块
        for (let i = 0; i < 3; i++) {
          const audio = AudioDataGenerator.generateValidAudio(800);
          const audioData: AudioData = {
            audio_data: audio,
            sample_rate: 16000,
            audio_format: 'pcm',
            source,
            timestamp: Date.now() + i * 800
          };
          
          await this.voiceRecognition.processAudioData(audioData);
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }
      
      // 检查状态
      const status = this.voiceRecognition.getStatus();
      if (!status.isActive) {
        throw new Error('语音识别应该处于活跃状态');
      }
      
      this.testResults.push({ name: '音频处理流程', passed: true });
      console.log('✅ 音频处理流程测试通过');
      
    } catch (error) {
      this.testResults.push({ 
        name: '音频处理流程', 
        passed: false, 
        error: error instanceof Error ? error.message : '未知错误' 
      });
      console.error('❌ 音频处理流程测试失败:', error);
    }
  }
  
  /**
   * 测试多音频源处理
   */
  private async testMultiSourceProcessing(): Promise<void> {
    try {
      console.log('🎤🔊 测试多音频源处理...');
      
      // 同时处理麦克风和系统音频
      const micAudio = AudioDataGenerator.generateValidAudio(1000);
      const sysAudio = AudioDataGenerator.generateValidAudio(1000);
      
      const micAudioData: AudioData = {
        audio_data: micAudio,
        sample_rate: 16000,
        audio_format: 'pcm',
        source: 'microphone',
        timestamp: Date.now()
      };
      
      const sysAudioData: AudioData = {
        audio_data: sysAudio,
        sample_rate: 16000,
        audio_format: 'pcm',
        source: 'system',
        timestamp: Date.now()
      };
      
      // 并发处理
      await Promise.all([
        this.voiceRecognition.processAudioData(micAudioData),
        this.voiceRecognition.processAudioData(sysAudioData)
      ]);
      
      this.testResults.push({ name: '多音频源处理', passed: true });
      console.log('✅ 多音频源处理测试通过');
      
    } catch (error) {
      this.testResults.push({ 
        name: '多音频源处理', 
        passed: false, 
        error: error instanceof Error ? error.message : '未知错误' 
      });
      console.error('❌ 多音频源处理测试失败:', error);
    }
  }
  
  /**
   * 测试性能监控
   */
  private async testPerformanceMonitoring(): Promise<void> {
    try {
      console.log('📊 测试性能监控...');
      
      // 获取性能状态
      const status = this.voiceRecognition.getStatus();
      
      if (typeof status.performanceMetrics.totalAudioProcessed !== 'number') {
        throw new Error('性能指标数据类型不正确');
      }
      
      console.log('性能指标:', status.performanceMetrics);
      
      this.testResults.push({ name: '性能监控', passed: true });
      console.log('✅ 性能监控测试通过');
      
    } catch (error) {
      this.testResults.push({ 
        name: '性能监控', 
        passed: false, 
        error: error instanceof Error ? error.message : '未知错误' 
      });
      console.error('❌ 性能监控测试失败:', error);
    }
  }
  
  /**
   * 打印测试结果
   */
  private printTestResults(): void {
    console.log('\n📋 测试结果汇总:');
    console.log('='.repeat(50));
    
    let passedCount = 0;
    let totalCount = this.testResults.length;
    
    this.testResults.forEach(result => {
      const status = result.passed ? '✅ 通过' : '❌ 失败';
      console.log(`${result.name}: ${status}`);
      
      if (!result.passed && result.error) {
        console.log(`   错误: ${result.error}`);
      }
      
      if (result.passed) passedCount++;
    });
    
    console.log('='.repeat(50));
    console.log(`总计: ${passedCount}/${totalCount} 个测试通过`);
    
    if (passedCount === totalCount) {
      console.log('🎉 所有测试都通过了！');
    } else {
      console.log(`⚠️ 有 ${totalCount - passedCount} 个测试失败`);
    }
  }
  
  /**
   * 清理测试资源
   */
  public async cleanup(): Promise<void> {
    console.log('🧹 清理测试资源...');
    await this.voiceRecognition.stopRecognition();
    this.voiceRecognition.cleanup();
    console.log('✅ 测试资源清理完成');
  }
}

/**
 * 运行测试的主函数
 */
export async function runVoiceRecognitionTests(): Promise<void> {
  const testSuite = new RealtimeVoiceRecognitionTestSuite();
  
  try {
    await testSuite.runAllTests();
  } finally {
    await testSuite.cleanup();
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runVoiceRecognitionTests().catch(console.error);
}
